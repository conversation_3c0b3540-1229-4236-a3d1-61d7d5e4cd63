import React from 'react';
import { ActionType } from './ActionType';
import PanGestureHandler from './web/handlers/PanGestureHandler';
import TapGestureHandler from './web/handlers/TapGestureHandler';
import LongPressGestureHandler from './web/handlers/LongPressGestureHandler';
import PinchGestureHandler from './web/handlers/PinchGestureHandler';
import RotationGestureHandler from './web/handlers/RotationGestureHandler';
import FlingGestureHandler from './web/handlers/FlingGestureHandler';
import NativeViewGestureHandler from './web/handlers/NativeViewGestureHandler';
import ManualGestureHandler from './web/handlers/ManualGestureHandler';
import { Config } from './web/interfaces';
export declare const Gestures: {
    NativeViewGestureHandler: typeof NativeViewGestureHandler;
    PanGestureHandler: typeof PanGestureHandler;
    TapGestureHandler: typeof TapGestureHandler;
    LongPressGestureHandler: typeof LongPressGestureHandler;
    PinchGestureHandler: typeof PinchGestureHandler;
    RotationGestureHandler: typeof RotationGestureHandler;
    FlingGestureHandler: typeof FlingGestureHandler;
    ManualGestureHandler: typeof ManualGestureHandler;
};
declare const _default: {
    handleSetJSResponder(_tag: number, _blockNativeResponder: boolean): void;
    handleClearJSResponder(): void;
    createGestureHandler<T>(_handlerName: keyof typeof Gestures, _handlerTag: number, _config: T): void;
    attachGestureHandler(_handlerTag: number, _newView: any, _actionType: ActionType, _propsRef: React.RefObject<unknown>): void;
    updateGestureHandler(_handlerTag: number, _newConfig: Config): void;
    getGestureHandlerNode(_handlerTag: number): void;
    dropGestureHandler(_handlerTag: number): void;
    flushOperations(): void;
};
export default _default;
