{"version": 3, "names": ["channelFromLrgb", "c", "abs", "Math", "sign", "pow", "convertLrgbToRgb", "r", "g", "b", "alpha", "channelToLrgb", "convertRgbToLrgb", "convert", "fromRgb", "toRgb"], "sourceRoot": "../../../src", "sources": ["culori/lrgb.ts"], "mappings": "AAAA,YAAY;;AAEZ;AACA;AACA;AACA;AACA;;AAEA;AACA;AAIA,MAAMA,eAAe,GAAGA,CAACC,CAAC,GAAG,CAAC,KAAK;EACjC,SAAS;;EACT,MAAMC,GAAG,GAAGC,IAAI,CAACD,GAAG,CAACD,CAAC,CAAC;EACvB,IAAIC,GAAG,GAAG,SAAS,EAAE;IACnB,OAAO,CAACC,IAAI,CAACC,IAAI,CAACH,CAAC,CAAC,IAAI,CAAC,KAAK,KAAK,GAAGE,IAAI,CAACE,GAAG,CAACH,GAAG,EAAE,CAAC,GAAG,GAAG,CAAC,GAAG,KAAK,CAAC;EACvE;EACA,OAAOD,CAAC,GAAG,KAAK;AAClB,CAAC;AAED,MAAMK,gBAAgB,GAAGA,CAAC;EAAEC,CAAC;EAAEC,CAAC;EAAEC,CAAC;EAAEC;AAAgB,CAAC,KAAe;EACnE,SAAS;;EACT,OAAO;IACLH,CAAC,EAAEP,eAAe,CAACO,CAAC,CAAC;IACrBC,CAAC,EAAER,eAAe,CAACQ,CAAC,CAAC;IACrBC,CAAC,EAAET,eAAe,CAACS,CAAC,CAAC;IACrBC;EACF,CAAC;AACH,CAAC;AAED,MAAMC,aAAa,GAAGA,CAACV,CAAC,GAAG,CAAC,KAAK;EAC/B,SAAS;;EACT,MAAMC,GAAG,GAAGC,IAAI,CAACD,GAAG,CAACD,CAAC,CAAC;EACvB,IAAIC,GAAG,IAAI,OAAO,EAAE;IAClB,OAAOD,CAAC,GAAG,KAAK;EAClB;EACA,OAAO,CAACE,IAAI,CAACC,IAAI,CAACH,CAAC,CAAC,IAAI,CAAC,IAAIE,IAAI,CAACE,GAAG,CAAC,CAACH,GAAG,GAAG,KAAK,IAAI,KAAK,EAAE,GAAG,CAAC;AACnE,CAAC;AAED,MAAMU,gBAAgB,GAAGA,CAAC;EAAEL,CAAC;EAAEC,CAAC;EAAEC,CAAC;EAAEC;AAAgB,CAAC,KAAK;EACzD,SAAS;;EACT,OAAO;IACLH,CAAC,EAAEI,aAAa,CAACJ,CAAC,CAAC;IACnBC,CAAC,EAAEG,aAAa,CAACH,CAAC,CAAC;IACnBC,CAAC,EAAEE,aAAa,CAACF,CAAC,CAAC;IACnBC;EACF,CAAC;AACH,CAAC;AAED,eAAe;EACbG,OAAO,EAAE;IACPC,OAAO,EAAEF,gBAAgB;IACzBG,KAAK,EAAET;EACT;AACF,CAAC", "ignoreList": []}