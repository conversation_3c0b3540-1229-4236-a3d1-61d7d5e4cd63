{"version": 3, "names": ["findHostInstance", "getInternalInstanceHandleFromPublicInstance", "getShadowNodeWrapperFromRef", "ref", "hostInstance", "undefined", "require", "_ref", "_internalInstanceHandle", "e", "scrollViewRef", "getScrollResponder", "getNativeScrollRef", "otherScrollViewRef", "textInputRef", "__internalInstanceHandle", "stateNode", "node", "resolvedRef", "instance"], "sourceRoot": "../../src", "sources": ["fabricUtils.ts"], "mappings": "AAAA,YAAY;;AACZ;AAGA,SACEA,gBAAgB,QAEX,sCAAsC;AAE7C,IAAIC,2CAEH;AAED,OAAO,SAASC,2BAA2BA,CACzCC,GAAoB,EACpBC,YAA2B,EACR;EACnB,IAAIH,2CAA2C,KAAKI,SAAS,EAAE;IAC7D,IAAI;MACFJ,2CAA2C,GACzCK,OAAO,CAAC,wFAAwF,CAAC,CAC9FL,2CAA2C,KAC5CM,IAAS,IAAKA,IAAI,CAACC,uBAAuB,CAAC;IACjD,CAAC,CAAC,OAAOC,CAAC,EAAE;MACVR,2CAA2C,GAAIM,IAAS,IACtDA,IAAI,CAACC,uBAAuB;IAChC;EACF;;EAEA;EACA;EACA;EACA,MAAME,aAAa,GAAGP,GAAG,EAAEQ,kBAAkB,GAAG,CAAC,EAAEC,kBAAkB,GAAG,CAAC;EACzE;EACA,MAAMC,kBAAkB,GAAGV,GAAG,EAAES,kBAAkB,GAAG,CAAC;EACtD;EACA,MAAME,YAAY,GAAGX,GAAG,EAAEY,wBAAwB,EAAEC,SAAS,EAAEC,IAAI;EAEnE,IAAIC,WAAW;EACf,IAAIR,aAAa,EAAE;IACjBQ,WAAW,GAAGR,aAAa,CAACK,wBAAwB,CAACC,SAAS,CAACC,IAAI;EACrE,CAAC,MAAM,IAAIJ,kBAAkB,EAAE;IAC7BK,WAAW,GAAGL,kBAAkB,CAACE,wBAAwB,CAACC,SAAS,CAACC,IAAI;EAC1E,CAAC,MAAM,IAAIH,YAAY,EAAE;IACvBI,WAAW,GAAGJ,YAAY;EAC5B,CAAC,MAAM;IACL,MAAMK,QAAQ,GAAGf,YAAY,IAAIJ,gBAAgB,CAACG,GAAG,CAAC;IACtDe,WAAW,GACTjB,2CAA2C,CAACkB,QAAQ,CAAC,CAACH,SAAS,CAACC,IAAI;EACxE;EAEA,OAAOC,WAAW;AACpB", "ignoreList": []}