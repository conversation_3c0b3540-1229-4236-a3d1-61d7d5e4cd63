{"version": 3, "names": ["executeOnUIRuntimeSync", "jsiConfigureProps", "ReanimatedError", "updateLoggerConfig", "shouldBeUseWeb", "PropsAllowlists", "SHOULD_BE_USE_WEB", "assertNoOverlapInLists", "key", "NATIVE_THREAD_PROPS_WHITELIST", "UI_THREAD_PROPS_WHITELIST", "configureProps", "Object", "keys", "addWhitelistedNativeProps", "props", "oldSize", "length", "addWhitelistedUIProps", "configure<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "config", "PROCESSED_VIEW_NAMES", "Set", "adaptViewConfig", "viewConfig", "viewName", "uiViewClassName", "validAttributes", "has", "propsToAdd", "for<PERSON>ach", "add"], "sourceRoot": "../../src", "sources": ["ConfigHelper.ts"], "mappings": "AAAA,YAAY;;AACZ,SAASA,sBAAsB,EAAEC,iBAAiB,QAAQ,WAAQ;AAClE,SAASC,eAAe,QAAQ,aAAU;AAE1C,SAASC,kBAAkB,QAAQ,mBAAU;AAC7C,SAASC,cAAc,QAAQ,sBAAmB;AAClD,SAASC,eAAe,QAAQ,sBAAmB;AAEnD,MAAMC,iBAAiB,GAAGF,cAAc,CAAC,CAAC;AAE1C,SAASG,sBAAsBA,CAAA,EAAG;EAChC,KAAK,MAAMC,GAAG,IAAIH,eAAe,CAACI,6BAA6B,EAAE;IAC/D,IAAID,GAAG,IAAIH,eAAe,CAACK,yBAAyB,EAAE;MACpD,MAAM,IAAIR,eAAe,CACvB,cAAcM,GAAG,wFACnB,CAAC;IACH;EACF;AACF;AAEA,OAAO,SAASG,cAAcA,CAAA,EAAS;EACrCJ,sBAAsB,CAAC,CAAC;EACxBN,iBAAiB,CACfW,MAAM,CAACC,IAAI,CAACR,eAAe,CAACK,yBAAyB,CAAC,EACtDE,MAAM,CAACC,IAAI,CAACR,eAAe,CAACI,6BAA6B,CAC3D,CAAC;AACH;AAEA,OAAO,SAASK,yBAAyBA,CACvCC,KAA8B,EACxB;EACN,MAAMC,OAAO,GAAGJ,MAAM,CAACC,IAAI,CACzBR,eAAe,CAACI,6BAClB,CAAC,CAACQ,MAAM;EACRZ,eAAe,CAACI,6BAA6B,GAAG;IAC9C,GAAGJ,eAAe,CAACI,6BAA6B;IAChD,GAAGM;EACL,CAAC;EACD,IACEC,OAAO,KACPJ,MAAM,CAACC,IAAI,CAACR,eAAe,CAACI,6BAA6B,CAAC,CAACQ,MAAM,EACjE;IACAN,cAAc,CAAC,CAAC;EAClB;AACF;AAEA,OAAO,SAASO,qBAAqBA,CAACH,KAA8B,EAAQ;EAC1E,MAAMC,OAAO,GAAGJ,MAAM,CAACC,IAAI,CAACR,eAAe,CAACK,yBAAyB,CAAC,CAACO,MAAM;EAC7EZ,eAAe,CAACK,yBAAyB,GAAG;IAC1C,GAAGL,eAAe,CAACK,yBAAyB;IAC5C,GAAGK;EACL,CAAC;EACD,IACEC,OAAO,KAAKJ,MAAM,CAACC,IAAI,CAACR,eAAe,CAACK,yBAAyB,CAAC,CAACO,MAAM,EACzE;IACAN,cAAc,CAAC,CAAC;EAClB;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASQ,yBAAyBA,CAACC,MAAoB,EAAE;EAC9D;EACAjB,kBAAkB,CAACiB,MAAM,CAAC;EAC1B;EACA,IAAI,CAACd,iBAAiB,EAAE;IACtBN,sBAAsB,CAACG,kBAAkB,CAAC,CAACiB,MAAM,CAAC;EACpD;AACF;AAEA,MAAMC,oBAAoB,GAAG,IAAIC,GAAG,CAAC,CAAC;AAMtC;AACA;AACA;AACA;;AAEA,OAAO,SAASC,eAAeA,CAACC,UAAsB,EAAQ;EAC5D,MAAMC,QAAQ,GAAGD,UAAU,CAACE,eAAe;EAC3C,MAAMX,KAAK,GAAGS,UAAU,CAACG,eAAe;;EAExC;EACA,IAAI,CAACN,oBAAoB,CAACO,GAAG,CAACH,QAAQ,CAAC,EAAE;IACvC,MAAMI,UAAmC,GAAG,CAAC,CAAC;IAC9CjB,MAAM,CAACC,IAAI,CAACE,KAAK,CAAC,CAACe,OAAO,CAAEtB,GAAG,IAAK;MAClC;MACA;MACA,IACE,EAAEA,GAAG,IAAIH,eAAe,CAACI,6BAA6B,CAAC,IACvD,EAAED,GAAG,IAAIH,eAAe,CAACK,yBAAyB,CAAC,EACnD;QACAmB,UAAU,CAACrB,GAAG,CAAC,GAAG,IAAI;MACxB;IACF,CAAC,CAAC;IACFU,qBAAqB,CAACW,UAAU,CAAC;IAEjCR,oBAAoB,CAACU,GAAG,CAACN,QAAQ,CAAC;EACpC;AACF;AAEAd,cAAc,CAAC,CAAC", "ignoreList": []}