{"version": 3, "names": ["ReanimatedFlatList", "FlatList", "AnimatedImage", "Image", "AnimatedScrollView", "ScrollView", "AnimatedText", "Text", "AnimatedView", "View", "addWhitelistedNativeProps", "addWhitelistedUIProps", "createAnimatedComponent"], "sourceRoot": "../../src", "sources": ["Animated.ts"], "mappings": "AAAA,YAAY;;AAmBZ,SAASA,kBAAkB,IAAIC,QAAQ,QAAQ,yBAAsB;AACrE,SAASC,aAAa,IAAIC,KAAK,QAAQ,sBAAmB;AAC1D,SAASC,kBAAkB,IAAIC,UAAU,QAAQ,2BAAwB;AACzE,SAASC,YAAY,IAAIC,IAAI,QAAQ,qBAAkB;AACvD,SAASC,YAAY,IAAIC,IAAI,QAAQ,qBAAkB;AACvD,SACEC,yBAAyB,EACzBC,qBAAqB,QAChB,mBAAgB;AACvB,SAASC,uBAAuB,QAAQ,oCAA2B;AACnE;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAGA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA", "ignoreList": []}