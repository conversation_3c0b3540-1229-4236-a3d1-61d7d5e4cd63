{"version": 3, "names": ["ColorProperties", "processColor", "logger", "withTiming", "defineAnimation", "isValidLayoutAnimationProp", "<PERSON><PERSON><PERSON>", "obj", "path", "keys", "Array", "isArray", "reduce", "acc", "current", "undefined", "set<PERSON>ath", "value", "currObj", "i", "length", "withStyleAnimation", "styleAnimations", "onFrame", "animation", "now", "stillGoing", "entriesToCheck", "currentEntry", "pop", "index", "push", "concat", "key", "Object", "currentStyleAnimation", "finished", "callback", "isAnimatingColorProp", "includes", "onStart", "previousAnimation", "prevAnimation", "prevVal", "__DEV__", "warn", "join", "propName", "trim", "currentAnimation", "duration", "animationsToCheck", "element", "values", "isHigherOrder"], "sourceRoot": "../../../src", "sources": ["animation/styleAnimation.ts"], "mappings": "AAAA,YAAY;;AACZ,SAASA,eAAe,EAAEC,YAAY,QAAQ,cAAW;AAUzD,SAASC,MAAM,QAAQ,oBAAW;AAElC,SAASC,UAAU,QAAQ,aAAU;AACrC,SAASC,eAAe,EAAEC,0BAA0B,QAAQ,WAAQ;;AAEpE;AACA;AACA,SAASC,WAAWA,CAClBC,GAAoB,EACpBC,IAAyC,EACN;EACnC,SAAS;;EACT,MAAMC,IAAuB,GAAGC,KAAK,CAACC,OAAO,CAACH,IAAI,CAAC,GAAGA,IAAI,GAAG,CAACA,IAAI,CAAC;EACnE,OAAOC,IAAI,CAACG,MAAM,CAAoC,CAACC,GAAG,EAAEC,OAAO,KAAK;IACtE,IAAIJ,KAAK,CAACC,OAAO,CAACE,GAAG,CAAC,IAAI,OAAOC,OAAO,KAAK,QAAQ,EAAE;MACrD,OAAOD,GAAG,CAACC,OAAO,CAAC;IACrB,CAAC,MAAM,IACLD,GAAG,KAAK,IAAI,IACZ,OAAOA,GAAG,KAAK,QAAQ,IACtBC,OAAO,IAAwBD,GAAG,EACnC;MACA,OAAQA,GAAG,CACTC,OAAO,CACR;IACH;IACA,OAAOC,SAAS;EAClB,CAAC,EAAER,GAAG,CAAC;AACT;;AAEA;;AAEA,SAASS,OAAOA,CACdT,GAAoB,EACpBC,IAAU,EACVS,KAA4B,EACtB;EACN,SAAS;;EACT,MAAMR,IAAU,GAAGC,KAAK,CAACC,OAAO,CAACH,IAAI,CAAC,GAAGA,IAAI,GAAG,CAACA,IAAI,CAAC;EACtD,IAAIU,OAA8B,GAAGX,GAAG;EACxC,KAAK,IAAIY,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGV,IAAI,CAACW,MAAM,GAAG,CAAC,EAAED,CAAC,EAAE,EAAE;IACxC;IACAD,OAAO,GAAGA,OAAmD;IAC7D,IAAI,EAAET,IAAI,CAACU,CAAC,CAAC,IAAID,OAAO,CAAC,EAAE;MACzB;MACA,IAAI,OAAOT,IAAI,CAACU,CAAC,GAAG,CAAC,CAAC,KAAK,QAAQ,EAAE;QACnCD,OAAO,CAACT,IAAI,CAACU,CAAC,CAAC,CAAC,GAAG,EAAE;MACvB,CAAC,MAAM;QACLD,OAAO,CAACT,IAAI,CAACU,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;MACvB;IACF;IACAD,OAAO,GAAGA,OAAO,CAACT,IAAI,CAACU,CAAC,CAAC,CAAC;EAC5B;EAECD,OAAO,CAA8CT,IAAI,CAACA,IAAI,CAACW,MAAM,GAAG,CAAC,CAAC,CAAC,GAC1EH,KAAK;AACT;AAOA,OAAO,SAASI,kBAAkBA,CAChCC,eAAmC,EACb;EACtB,SAAS;;EACT,OAAOlB,eAAe,CAAuB,CAAC,CAAC,EAAE,MAAM;IACrD,SAAS;;IAET,MAAMmB,OAAO,GAAGA,CACdC,SAA+B,EAC/BC,GAAc,KACF;MACZ,IAAIC,UAAU,GAAG,KAAK;MACtB,MAAMC,cAAoD,GAAG,CAC3D;QAAEV,KAAK,EAAEO,SAAS,CAACF,eAAe;QAAEd,IAAI,EAAE;MAAG,CAAC,CAC/C;MACD,OAAOmB,cAAc,CAACP,MAAM,GAAG,CAAC,EAAE;QAChC,MAAMQ,YAAgD,GACpDD,cAAc,CAACE,GAAG,CAAC,CAAuC;QAC5D,IAAInB,KAAK,CAACC,OAAO,CAACiB,YAAY,CAACX,KAAK,CAAC,EAAE;UACrC,KAAK,IAAIa,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGF,YAAY,CAACX,KAAK,CAACG,MAAM,EAAEU,KAAK,EAAE,EAAE;YAC9DH,cAAc,CAACI,IAAI,CAAC;cAClBd,KAAK,EAAEW,YAAY,CAACX,KAAK,CAACa,KAAK,CAAC;cAChCtB,IAAI,EAAEoB,YAAY,CAACpB,IAAI,CAACwB,MAAM,CAACF,KAAK;YACtC,CAAC,CAAC;UACJ;QACF,CAAC,MAAM,IACL,OAAOF,YAAY,CAACX,KAAK,KAAK,QAAQ,IACtCW,YAAY,CAACX,KAAK,CAACM,OAAO,KAAKR,SAAS,EACxC;UACA;UACA,KAAK,MAAMkB,GAAG,IAAIC,MAAM,CAACzB,IAAI,CAACmB,YAAY,CAACX,KAAK,CAAC,EAAE;YACjDU,cAAc,CAACI,IAAI,CAAC;cAClBd,KAAK,EAAEW,YAAY,CAACX,KAAK,CAACgB,GAAG,CAAC;cAC9BzB,IAAI,EAAEoB,YAAY,CAACpB,IAAI,CAACwB,MAAM,CAACC,GAAG;YACpC,CAAC,CAAC;UACJ;QACF,CAAC,MAAM;UACL,MAAME,qBAAsC,GAC1CP,YAAY,CAACX,KAAwB;UACvC,IAAIkB,qBAAqB,CAACC,QAAQ,EAAE;YAClC;UACF;UACA,MAAMA,QAAQ,GAAGD,qBAAqB,CAACZ,OAAO,CAC5CY,qBAAqB,EACrBV,GACF,CAAC;UACD,IAAIW,QAAQ,EAAE;YACZD,qBAAqB,CAACC,QAAQ,GAAG,IAAI;YACrC,IAAID,qBAAqB,CAACE,QAAQ,EAAE;cAClCF,qBAAqB,CAACE,QAAQ,CAAC,IAAI,CAAC;YACtC;UACF,CAAC,MAAM;YACLX,UAAU,GAAG,IAAI;UACnB;;UAEA;UACA;UACA,MAAMY,oBAAoB,GAAGtC,eAAe,CAACuC,QAAQ,CACnDX,YAAY,CAACpB,IAAI,CAAC,CAAC,CACrB,CAAC;UAEDQ,OAAO,CACLQ,SAAS,CAACV,OAAO,EACjBc,YAAY,CAACpB,IAAI,EACjB8B,oBAAoB,GAChBrC,YAAY,CAACkC,qBAAqB,CAACrB,OAAO,CAAC,GAC3CqB,qBAAqB,CAACrB,OAC5B,CAAC;QACH;MACF;MACA,OAAO,CAACY,UAAU;IACpB,CAAC;IAED,MAAMc,OAAO,GAAGA,CACdhB,SAA+B,EAC/BP,KAAyB,EACzBQ,GAAc,EACdgB,iBAAuC,KAC9B;MACT,MAAMd,cAEH,GAAG,CAAC;QAAEV,KAAK,EAAEK,eAAe;QAAEd,IAAI,EAAE;MAAG,CAAC,CAAC;MAC5C,OAAOmB,cAAc,CAACP,MAAM,GAAG,CAAC,EAAE;QAChC,MAAMQ,YAEL,GAAGD,cAAc,CAACE,GAAG,CAAC,CAEtB;QACD,IAAInB,KAAK,CAACC,OAAO,CAACiB,YAAY,CAACX,KAAK,CAAC,EAAE;UACrC,KAAK,IAAIa,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGF,YAAY,CAACX,KAAK,CAACG,MAAM,EAAEU,KAAK,EAAE,EAAE;YAC9DH,cAAc,CAACI,IAAI,CAAC;cAClBd,KAAK,EAAEW,YAAY,CAACX,KAAK,CAACa,KAAK,CAAC;cAChCtB,IAAI,EAAEoB,YAAY,CAACpB,IAAI,CAACwB,MAAM,CAACF,KAAK;YACtC,CAAC,CAAC;UACJ;QACF,CAAC,MAAM,IACL,OAAOF,YAAY,CAACX,KAAK,KAAK,QAAQ,IACtCW,YAAY,CAACX,KAAK,CAACuB,OAAO,KAAKzB,SAAS,EACxC;UACA,KAAK,MAAMkB,GAAG,IAAIC,MAAM,CAACzB,IAAI,CAACmB,YAAY,CAACX,KAAK,CAAC,EAAE;YACjDU,cAAc,CAACI,IAAI,CAAC;cAClBd,KAAK,EAAEW,YAAY,CAACX,KAAK,CAACgB,GAAG,CAAC;cAC9BzB,IAAI,EAAEoB,YAAY,CAACpB,IAAI,CAACwB,MAAM,CAACC,GAAG;YACpC,CAAC,CAAC;UACJ;QACF,CAAC,MAAM;UACL,MAAMS,aAAa,GAAGpC,WAAW,CAC/BmC,iBAAiB,EAAEnB,eAAe,EAClCM,YAAY,CAACpB,IACf,CAAC;UACD,IAAImC,OAAO,GAAGrC,WAAW,CAACW,KAAK,EAAEW,YAAY,CAACpB,IAAI,CAAC;UACnD,IAAIkC,aAAa,IAAI,CAACC,OAAO,EAAE;YAC7BA,OAAO,GAAID,aAAa,CAAS5B,OAAO;UAC1C;UACA,IAAI8B,OAAO,EAAE;YACX,IAAID,OAAO,KAAK5B,SAAS,EAAE;cACzBb,MAAM,CAAC2C,IAAI,CACT,yDAAyDjB,YAAY,CAACpB,IAAI,CAACsC,IAAI,CAC7E,GACF,CAAC,EACH,CAAC;YACH;YACA,MAAMC,QAAQ,GAAGnB,YAAY,CAACpB,IAAI,CAAC,CAAC,CAAC;YACrC,IACE,OAAOuC,QAAQ,KAAK,QAAQ,IAC5B,CAAC1C,0BAA0B,CAAC0C,QAAQ,CAACC,IAAI,CAAC,CAAC,CAAC,EAC5C;cACA9C,MAAM,CAAC2C,IAAI,CACT,IAAIE,QAAQ,4FACd,CAAC;YACH;UACF;UACA/B,OAAO,CAACQ,SAAS,CAACV,OAAO,EAAEc,YAAY,CAACpB,IAAI,EAAEmC,OAAO,CAAC;UACtD,IAAIM,gBAAiC;UACrC,IACE,OAAOrB,YAAY,CAACX,KAAK,KAAK,QAAQ,IACtC,CAACW,YAAY,CAACX,KAAK,CAACuB,OAAO,EAC3B;YACAS,gBAAgB,GAAG9C,UAAU,CAC3ByB,YAAY,CAACX,KAAK,EAClB;cAAEiC,QAAQ,EAAE;YAAE,CAChB,CAAoB,CAAC,CAAC;YACtBlC,OAAO,CACLQ,SAAS,CAACF,eAAe,EACzBM,YAAY,CAACpB,IAAI,EACjByC,gBACF,CAAC;UACH,CAAC,MAAM;YACLA,gBAAgB,GAAGrB,YAAY,CAACX,KAAmC;UACrE;UACAgC,gBAAgB,CAACT,OAAO,CACtBS,gBAAgB,EAChBN,OAAO,EACPlB,GAAG,EACHiB,aACF,CAAC;QACH;MACF;IACF,CAAC;IAED,MAAML,QAAQ,GAAID,QAAiB,IAAW;MAC5C,IAAI,CAACA,QAAQ,EAAE;QACb,MAAMe,iBAAwD,GAAG,CAC/D7B,eAAe,CAChB;QACD,OAAO6B,iBAAiB,CAAC/B,MAAM,GAAG,CAAC,EAAE;UACnC,MAAM6B,gBAAqD,GACzDE,iBAAiB,CAACtB,GAAG,CAAC,CAAwC;UAChE,IAAInB,KAAK,CAACC,OAAO,CAACsC,gBAAgB,CAAC,EAAE;YACnC,KAAK,MAAMG,OAAO,IAAIH,gBAAgB,EAAE;cACtCE,iBAAiB,CAACpB,IAAI,CAACqB,OAAO,CAAC;YACjC;UACF,CAAC,MAAM,IACL,OAAOH,gBAAgB,KAAK,QAAQ,IACpCA,gBAAgB,CAACT,OAAO,KAAKzB,SAAS,EACtC;YACA,KAAK,MAAME,KAAK,IAAIiB,MAAM,CAACmB,MAAM,CAACJ,gBAAgB,CAAC,EAAE;cACnDE,iBAAiB,CAACpB,IAAI,CAACd,KAAK,CAAC;YAC/B;UACF,CAAC,MAAM;YACL,MAAMkB,qBAAsC,GAC1Cc,gBAAmC;YACrC,IACE,CAACd,qBAAqB,CAACC,QAAQ,IAC/BD,qBAAqB,CAACE,QAAQ,EAC9B;cACAF,qBAAqB,CAACE,QAAQ,CAAC,KAAK,CAAC;YACvC;UACF;QACF;MACF;IACF,CAAC;IAED,OAAO;MACLiB,aAAa,EAAE,IAAI;MACnB/B,OAAO;MACPiB,OAAO;MACP1B,OAAO,EAAE,CAAC,CAAC;MACXQ,eAAe;MACfe;IACF,CAAC;EACH,CAAC,CAAC;AACJ", "ignoreList": []}