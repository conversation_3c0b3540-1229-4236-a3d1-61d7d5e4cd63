{"version": 3, "names": ["calculateNewMassToMatchDuration", "checkIfConfigIsValid", "criticallyDampedSpringCalculations", "initialCalculations", "isAnimationTerminatingCalculation", "scaleZetaToMatchClamps", "underDampedSpringCalculations", "defineAnimation", "getReduceMotionForAnimation", "with<PERSON><PERSON><PERSON>", "toValue", "userConfig", "callback", "defaultConfig", "damping", "mass", "stiffness", "overshootClamping", "restDisplacementThreshold", "restSpeedThreshold", "velocity", "duration", "dampingRatio", "reduceMotion", "undefined", "clamp", "config", "useDuration", "skipAnimation", "springOnFrame", "animation", "now", "startTimestamp", "current", "timeFromStart", "lastTimestamp", "deltaTime", "Math", "min", "t", "v0", "x0", "zeta", "omega0", "omega1", "position", "newPosition", "newVelocity", "isOvershooting", "isVelocity", "isDisplacement", "springIsNotInMove", "isTriggeredTwice", "previousAnimation", "onStart", "value", "startValue", "triggeredTwice", "Number", "actualDuration", "onFrame"], "sourceRoot": "../../../src", "sources": ["animation/spring.ts"], "mappings": "AAAA,YAAY;;AAcZ,SACEA,+BAA+B,EAC/BC,oBAAoB,EACpBC,kCAAkC,EAClCC,mBAAmB,EACnBC,iCAAiC,EACjCC,sBAAsB,EACtBC,6BAA6B,QACxB,kBAAe;AACtB,SAASC,eAAe,EAAEC,2BAA2B,QAAQ,WAAQ;;AAErE;;AAOA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,UAAU,GAAIA,CACzBC,OAAwB,EACxBC,UAAyB,EACzBC,QAA4B,KACG;EAC/B,SAAS;;EAET,OAAOL,eAAe,CAAkBG,OAAO,EAAE,MAAM;IACrD,SAAS;;IACT,MAAMG,aAAkC,GAAG;MACzCC,OAAO,EAAE,EAAE;MACXC,IAAI,EAAE,CAAC;MACPC,SAAS,EAAE,GAAG;MACdC,iBAAiB,EAAE,KAAK;MACxBC,yBAAyB,EAAE,IAAI;MAC/BC,kBAAkB,EAAE,CAAC;MACrBC,QAAQ,EAAE,CAAC;MACXC,QAAQ,EAAE,IAAI;MACdC,YAAY,EAAE,GAAG;MACjBC,YAAY,EAAEC,SAAS;MACvBC,KAAK,EAAED;IACT,CAAU;IAEV,MAAME,MAA+C,GAAG;MACtD,GAAGb,aAAa;MAChB,GAAGF,UAAU;MACbgB,WAAW,EAAE,CAAC,EAAEhB,UAAU,EAAEU,QAAQ,IAAIV,UAAU,EAAEW,YAAY,CAAC;MACjEM,aAAa,EAAE;IACjB,CAAC;IAEDF,MAAM,CAACE,aAAa,GAAG,CAAC3B,oBAAoB,CAACyB,MAAM,CAAC;IAEpD,IAAIA,MAAM,CAACL,QAAQ,KAAK,CAAC,EAAE;MACzBK,MAAM,CAACE,aAAa,GAAG,IAAI;IAC7B;IAEA,SAASC,aAAaA,CACpBC,SAA+B,EAC/BC,GAAc,EACL;MACT;MACA,MAAM;QAAErB,OAAO;QAAEsB,cAAc;QAAEC;MAAQ,CAAC,GAAGH,SAAS;MAEtD,MAAMI,aAAa,GAAGH,GAAG,GAAGC,cAAc;MAE1C,IAAIN,MAAM,CAACC,WAAW,IAAIO,aAAa,IAAIR,MAAM,CAACL,QAAQ,EAAE;QAC1DS,SAAS,CAACG,OAAO,GAAGvB,OAAO;QAC3B;QACAoB,SAAS,CAACK,aAAa,GAAG,CAAC;QAC3B,OAAO,IAAI;MACb;MAEA,IAAIT,MAAM,CAACE,aAAa,EAAE;QACxBE,SAAS,CAACG,OAAO,GAAGvB,OAAO;QAC3BoB,SAAS,CAACK,aAAa,GAAG,CAAC;QAC3B,OAAO,IAAI;MACb;MACA,MAAM;QAAEA,aAAa;QAAEf;MAAS,CAAC,GAAGU,SAAS;MAE7C,MAAMM,SAAS,GAAGC,IAAI,CAACC,GAAG,CAACP,GAAG,GAAGI,aAAa,EAAE,EAAE,CAAC;MACnDL,SAAS,CAACK,aAAa,GAAGJ,GAAG;MAE7B,MAAMQ,CAAC,GAAGH,SAAS,GAAG,IAAI;MAC1B,MAAMI,EAAE,GAAG,CAACpB,QAAQ;MACpB,MAAMqB,EAAE,GAAG/B,OAAO,GAAGuB,OAAO;MAE5B,MAAM;QAAES,IAAI;QAAEC,MAAM;QAAEC;MAAO,CAAC,GAAGd,SAAS;MAE1C,MAAM;QAAEe,QAAQ,EAAEC,WAAW;QAAE1B,QAAQ,EAAE2B;MAAY,CAAC,GACpDL,IAAI,GAAG,CAAC,GACJpC,6BAA6B,CAACwB,SAAS,EAAE;QACvCY,IAAI;QACJF,EAAE;QACFC,EAAE;QACFE,MAAM;QACNC,MAAM;QACNL;MACF,CAAC,CAAC,GACFrC,kCAAkC,CAAC4B,SAAS,EAAE;QAC5CU,EAAE;QACFC,EAAE;QACFE,MAAM;QACNJ;MACF,CAAC,CAAC;MAERT,SAAS,CAACG,OAAO,GAAGa,WAAW;MAC/BhB,SAAS,CAACV,QAAQ,GAAG2B,WAAW;MAEhC,MAAM;QAAEC,cAAc;QAAEC,UAAU;QAAEC;MAAe,CAAC,GAClD9C,iCAAiC,CAAC0B,SAAS,EAAEJ,MAAM,CAAC;MAEtD,MAAMyB,iBAAiB,GACrBH,cAAc,IAAKC,UAAU,IAAIC,cAAe;MAElD,IAAI,CAACxB,MAAM,CAACC,WAAW,IAAIwB,iBAAiB,EAAE;QAC5CrB,SAAS,CAACV,QAAQ,GAAG,CAAC;QACtBU,SAAS,CAACG,OAAO,GAAGvB,OAAO;QAC3B;QACAoB,SAAS,CAACK,aAAa,GAAG,CAAC;QAC3B,OAAO,IAAI;MACb;MAEA,OAAO,KAAK;IACd;IAEA,SAASiB,gBAAgBA,CACvBC,iBAA8C,EAC9CvB,SAA0B,EAC1B;MACA,OACEuB,iBAAiB,EAAElB,aAAa,IAChCkB,iBAAiB,EAAErB,cAAc,IACjCqB,iBAAiB,EAAE3C,OAAO,KAAKoB,SAAS,CAACpB,OAAO,IAChD2C,iBAAiB,EAAEhC,QAAQ,KAAKS,SAAS,CAACT,QAAQ,IAClDgC,iBAAiB,EAAE/B,YAAY,KAAKQ,SAAS,CAACR,YAAY;IAE9D;IAEA,SAASgC,OAAOA,CACdxB,SAA0B,EAC1ByB,KAAa,EACbxB,GAAc,EACdsB,iBAA8C,EACxC;MACNvB,SAAS,CAACG,OAAO,GAAGsB,KAAK;MACzBzB,SAAS,CAAC0B,UAAU,GAAGD,KAAK;MAE5B,IAAIxC,IAAI,GAAGW,MAAM,CAACX,IAAI;MACtB,MAAM0C,cAAc,GAAGL,gBAAgB,CAACC,iBAAiB,EAAEvB,SAAS,CAAC;MAErE,MAAMT,QAAQ,GAAGK,MAAM,CAACL,QAAQ;MAEhC,MAAMoB,EAAE,GAAGgB,cAAc;MACrB;MACA;MACAJ,iBAAiB,EAAEG,UAAU,GAC7BE,MAAM,CAAC5B,SAAS,CAACpB,OAAO,CAAC,GAAG6C,KAAK;MAErC,IAAIF,iBAAiB,EAAE;QACrBvB,SAAS,CAACV,QAAQ,GAChB,CAACqC,cAAc,GACXJ,iBAAiB,EAAEjC,QAAQ,GAC3BiC,iBAAiB,EAAEjC,QAAQ,GAAGM,MAAM,CAACN,QAAQ,KAAK,CAAC;MAC3D,CAAC,MAAM;QACLU,SAAS,CAACV,QAAQ,GAAGM,MAAM,CAACN,QAAQ,IAAI,CAAC;MAC3C;MAEA,IAAIqC,cAAc,EAAE;QAClB3B,SAAS,CAACY,IAAI,GAAGW,iBAAiB,EAAEX,IAAI,IAAI,CAAC;QAC7CZ,SAAS,CAACa,MAAM,GAAGU,iBAAiB,EAAEV,MAAM,IAAI,CAAC;QACjDb,SAAS,CAACc,MAAM,GAAGS,iBAAiB,EAAET,MAAM,IAAI,CAAC;MACnD,CAAC,MAAM;QACL,IAAIlB,MAAM,CAACC,WAAW,EAAE;UACtB,MAAMgC,cAAc,GAAGF,cAAc;UACjC;UACA;UACApC,QAAQ,IACP,CAACgC,iBAAiB,EAAElB,aAAa,IAAI,CAAC,KACpCkB,iBAAiB,EAAErB,cAAc,IAAI,CAAC,CAAC,CAAC,GAC3CX,QAAQ;UAEZK,MAAM,CAACL,QAAQ,GAAGsC,cAAc;UAChC5C,IAAI,GAAGf,+BAA+B,CACpCyC,EAAE,EACFf,MAAM,EACNI,SAAS,CAACV,QACZ,CAAC;QACH;QAEA,MAAM;UAAEsB,IAAI;UAAEC,MAAM;UAAEC;QAAO,CAAC,GAAGzC,mBAAmB,CAACY,IAAI,EAAEW,MAAM,CAAC;QAClEI,SAAS,CAACY,IAAI,GAAGA,IAAI;QACrBZ,SAAS,CAACa,MAAM,GAAGA,MAAM;QACzBb,SAAS,CAACc,MAAM,GAAGA,MAAM;QAEzB,IAAIlB,MAAM,CAACD,KAAK,KAAKD,SAAS,EAAE;UAC9BM,SAAS,CAACY,IAAI,GAAGrC,sBAAsB,CAACyB,SAAS,EAAEJ,MAAM,CAACD,KAAK,CAAC;QAClE;MACF;MAEAK,SAAS,CAACK,aAAa,GAAGkB,iBAAiB,EAAElB,aAAa,IAAIJ,GAAG;MAEjED,SAAS,CAACE,cAAc,GAAGyB,cAAc,GACrCJ,iBAAiB,EAAErB,cAAc,IAAID,GAAG,GACxCA,GAAG;IACT;IAEA,OAAO;MACL6B,OAAO,EAAE/B,aAAa;MACtByB,OAAO;MACP5C,OAAO;MACPU,QAAQ,EAAEM,MAAM,CAACN,QAAQ,IAAI,CAAC;MAC9Ba,OAAO,EAAEvB,OAAO;MAChB8C,UAAU,EAAE,CAAC;MACb5C,QAAQ;MACRuB,aAAa,EAAE,CAAC;MAChBH,cAAc,EAAE,CAAC;MACjBU,IAAI,EAAE,CAAC;MACPC,MAAM,EAAE,CAAC;MACTC,MAAM,EAAE,CAAC;MACTrB,YAAY,EAAEf,2BAA2B,CAACkB,MAAM,CAACH,YAAY;IAC/D,CAAC;EACH,CAAC,CAAC;AACJ,CAAoB", "ignoreList": []}