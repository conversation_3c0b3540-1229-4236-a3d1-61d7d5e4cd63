{"version": 3, "names": ["SensorType", "ReanimatedError", "logger", "mockedRequestAnimationFrame", "isChromeDebugger", "isJest", "isWeb", "isWindowAvailable", "WorkletsModule", "createJSReanimatedModule", "JSReanimated", "requestAnimationFrameImpl", "globalThis", "requestAnimationFrame", "workletsModule", "nextSensorId", "sensors", "Map", "platform", "undefined", "scheduleOnUI", "worklet", "createWorkletRuntime", "_name", "_initializer", "scheduleOnRuntime", "registerEventHandler", "_event<PERSON><PERSON><PERSON>", "_eventName", "_emitterReactTag", "unregisterEventHandler", "_", "enableLayoutAnimations", "warn", "configureLayoutAnimationBatch", "setShouldAnimateExitingForTag", "registerSensor", "sensorType", "interval", "_iosReferenceFrame", "<PERSON><PERSON><PERSON><PERSON>", "detectPlatform", "getSensorName", "window", "location", "protocol", "Platform", "WEB_IOS", "sensor", "initializeSensor", "addEventListener", "getSensorCallback", "start", "set", "ACCELEROMETER", "GRAVITY", "x", "y", "z", "WEB_ANDROID", "interfaceOrientation", "GYROSCOPE", "MAGNETIC_FIELD", "ROTATION", "qw", "qx", "qy", "qz", "quaternion", "yaw", "Math", "atan2", "pitch", "sin", "roll", "unregisterSensor", "id", "get", "stop", "delete", "subscribeForKeyboardEvents", "unsubscribeFromKeyboardEvents", "config", "referenceFrame", "frequency", "Accelerometer", "Gyroscope", "GravitySensor", "Magnetometer", "AbsoluteOrientationSensor", "userAgent", "navigator", "vendor", "opera", "UNKNOWN", "test", "WEB", "getViewProp", "_viewTag", "_propName", "_component", "_callback", "configureProps", "executeOnUIRuntimeSync", "_shareable", "markNodeAsRemovable", "_shadowNodeWrapper", "unmarkNodeAsRemovable"], "sourceRoot": "../../../../src", "sources": ["ReanimatedModule/js-reanimated/JSReanimated.ts"], "mappings": "AAAA,YAAY;;AAUZ,SAASA,UAAU,QAAQ,sBAAmB;AAC9C,SAASC,eAAe,QAAQ,iBAAc;AAC9C,SAASC,MAAM,QAAQ,uBAAc;AACrC,SAASC,2BAA2B,QAAQ,sCAAmC;AAC/E,SACEC,gBAAgB,EAChBC,MAAM,EACNC,KAAK,EACLC,iBAAiB,QACZ,0BAAuB;AAE9B,SAASC,cAAc,QAAQ,yBAAgB;AAG/C,OAAO,SAASC,wBAAwBA,CAAA,EAAsB;EAC5D,OAAO,IAAIC,YAAY,CAAC,CAAC;AAC3B;;AAEA;AACA;AACA;AACA,MAAMC,yBAAyB,GAC7BN,MAAM,CAAC,CAAC,IAAI,CAACO,UAAU,CAACC,qBAAqB,GACzCV,2BAA2B,GAC3BS,UAAU,CAACC,qBAAqB;AAEtC,MAAMH,YAAY,CAA8B;EAC9C;AACF;AACA;AACA;EACE,CAACI,cAAc,GAAoBN,cAAc;EACjDO,YAAY,GAAG,CAAC;EAChBC,OAAO,GAAG,IAAIC,GAAG,CAAoB,CAAC;EACtCC,QAAQ,GAAcC,SAAS;EAE/BC,YAAYA,CAAIC,OAAwB,EAAE;IACxC;IACAV,yBAAyB,CAACU,OAAO,CAAC;EACpC;EAEAC,oBAAoBA,CAClBC,KAAa,EACbC,YAAsC,EACtB;IAChB,MAAM,IAAIvB,eAAe,CACvB,wDACF,CAAC;EACH;EAEAwB,iBAAiBA,CAAA,EAAG;IAClB,MAAM,IAAIxB,eAAe,CACvB,qDACF,CAAC;EACH;EAEAyB,oBAAoBA,CAClBC,aAA8B,EAC9BC,UAAkB,EAClBC,gBAAwB,EAChB;IACR,MAAM,IAAI5B,eAAe,CACvB,wDACF,CAAC;EACH;EAEA6B,sBAAsBA,CAACC,CAAS,EAAQ;IACtC,MAAM,IAAI9B,eAAe,CACvB,0DACF,CAAC;EACH;EAEA+B,sBAAsBA,CAAA,EAAG;IACvB,IAAI1B,KAAK,CAAC,CAAC,EAAE;MACXJ,MAAM,CAAC+B,IAAI,CAAC,iDAAiD,CAAC;IAChE,CAAC,MAAM,IAAI5B,MAAM,CAAC,CAAC,EAAE;MACnBH,MAAM,CAAC+B,IAAI,CAAC,+CAA+C,CAAC;IAC9D,CAAC,MAAM,IAAI7B,gBAAgB,CAAC,CAAC,EAAE;MAC7BF,MAAM,CAAC+B,IAAI,CAAC,0DAA0D,CAAC;IACzE,CAAC,MAAM;MACL/B,MAAM,CAAC+B,IAAI,CAAC,4DAA4D,CAAC;IAC3E;EACF;EAEAC,6BAA6BA,CAAA,EAAG;IAC9B;EAAA;EAGFC,6BAA6BA,CAAA,EAAG;IAC9B;EAAA;EAGFC,cAAcA,CACZC,UAAsB,EACtBC,QAAgB,EAChBC,kBAA0B,EAC1BC,YAAmE,EAC3D;IACR,IAAI,CAACjC,iBAAiB,CAAC,CAAC,EAAE;MACxB;MACA;MACA,OAAO,CAAC,CAAC;IACX;IAEA,IAAI,IAAI,CAACW,QAAQ,KAAKC,SAAS,EAAE;MAC/B,IAAI,CAACsB,cAAc,CAAC,CAAC;IACvB;IAEA,IAAI,EAAE,IAAI,CAACC,aAAa,CAACL,UAAU,CAAC,IAAIM,MAAM,CAAC,EAAE;MAC/C;MACAzC,MAAM,CAAC+B,IAAI,CACT,0BAA0B,IACvB3B,KAAK,CAAC,CAAC,IAAIsC,QAAQ,CAACC,QAAQ,KAAK,QAAQ,GACtC,uEAAuE,GACvE,EAAE,CAAC,IACN,IAAI,CAAC3B,QAAQ,KAAK4B,QAAQ,CAACC,OAAO,GAC/B,oLAAoL,GACpL,EAAE,CACV,CAAC;MACD,OAAO,CAAC,CAAC;IACX;IAEA,IAAI,IAAI,CAAC7B,QAAQ,KAAKC,SAAS,EAAE;MAC/B,IAAI,CAACsB,cAAc,CAAC,CAAC;IACvB;IAEA,MAAMO,MAAiB,GAAG,IAAI,CAACC,gBAAgB,CAACZ,UAAU,EAAEC,QAAQ,CAAC;IACrEU,MAAM,CAACE,gBAAgB,CACrB,SAAS,EACT,IAAI,CAACC,iBAAiB,CAACH,MAAM,EAAEX,UAAU,EAAEG,YAAY,CACzD,CAAC;IACDQ,MAAM,CAACI,KAAK,CAAC,CAAC;IAEd,IAAI,CAACpC,OAAO,CAACqC,GAAG,CAAC,IAAI,CAACtC,YAAY,EAAEiC,MAAM,CAAC;IAC3C,OAAO,IAAI,CAACjC,YAAY,EAAE;EAC5B;EAEAoC,iBAAiB,GAAGA,CAClBH,MAAiB,EACjBX,UAAsB,EACtBG,YAAmE,KAChE;IACH,QAAQH,UAAU;MAChB,KAAKrC,UAAU,CAACsD,aAAa;MAC7B,KAAKtD,UAAU,CAACuD,OAAO;QACrB,OAAO,MAAM;UACX,IAAI;YAAEC,CAAC;YAAEC,CAAC;YAAEC;UAAE,CAAC,GAAGV,MAAM;;UAExB;UACA,IAAI,IAAI,CAAC9B,QAAQ,KAAK4B,QAAQ,CAACa,WAAW,EAAE;YAC1C,CAACH,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAAC,GAAG,CAAC,CAACF,CAAC,EAAE,CAACC,CAAC,EAAE,CAACC,CAAC,CAAC;UAC1B;UACA;UACClB,YAAY,CAAS;YAAEgB,CAAC;YAAEC,CAAC;YAAEC,CAAC;YAAEE,oBAAoB,EAAE;UAAE,CAAC,CAAC;QAC7D,CAAC;MACH,KAAK5D,UAAU,CAAC6D,SAAS;MACzB,KAAK7D,UAAU,CAAC8D,cAAc;QAC5B,OAAO,MAAM;UACX,MAAM;YAAEN,CAAC;YAAEC,CAAC;YAAEC;UAAE,CAAC,GAAGV,MAAM;UAC1B;UACCR,YAAY,CAAS;YAAEgB,CAAC;YAAEC,CAAC;YAAEC,CAAC;YAAEE,oBAAoB,EAAE;UAAE,CAAC,CAAC;QAC7D,CAAC;MACH,KAAK5D,UAAU,CAAC+D,QAAQ;QACtB,OAAO,MAAM;UACX,IAAI,CAACC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,CAAC,GAAGnB,MAAM,CAACoB,UAAU;;UAExC;UACA,IAAI,IAAI,CAAClD,QAAQ,KAAK4B,QAAQ,CAACa,WAAW,EAAE;YAC1C,CAACO,EAAE,EAAEC,EAAE,CAAC,GAAG,CAACA,EAAE,EAAE,CAACD,EAAE,CAAC;UACtB;;UAEA;UACA,MAAMG,GAAG,GAAG,CAACC,IAAI,CAACC,KAAK,CACrB,GAAG,IAAIL,EAAE,GAAGC,EAAE,GAAGH,EAAE,GAAGC,EAAE,CAAC,EACzBD,EAAE,GAAGA,EAAE,GAAGC,EAAE,GAAGA,EAAE,GAAGC,EAAE,GAAGA,EAAE,GAAGC,EAAE,GAAGA,EACrC,CAAC;UACD,MAAMK,KAAK,GAAGF,IAAI,CAACG,GAAG,CAAC,CAAC,GAAG,IAAIR,EAAE,GAAGE,EAAE,GAAGH,EAAE,GAAGE,EAAE,CAAC,CAAC;UAClD,MAAMQ,IAAI,GAAG,CAACJ,IAAI,CAACC,KAAK,CACtB,GAAG,IAAIN,EAAE,GAAGC,EAAE,GAAGF,EAAE,GAAGG,EAAE,CAAC,EACzBH,EAAE,GAAGA,EAAE,GAAGC,EAAE,GAAGA,EAAE,GAAGC,EAAE,GAAGA,EAAE,GAAGC,EAAE,GAAGA,EACrC,CAAC;UACD;UACC3B,YAAY,CAAS;YACpBwB,EAAE;YACFC,EAAE;YACFC,EAAE;YACFC,EAAE;YACFE,GAAG;YACHG,KAAK;YACLE,IAAI;YACJd,oBAAoB,EAAE;UACxB,CAAC,CAAC;QACJ,CAAC;IACL;EACF,CAAC;EAEDe,gBAAgBA,CAACC,EAAU,EAAQ;IACjC,MAAM5B,MAA6B,GAAG,IAAI,CAAChC,OAAO,CAAC6D,GAAG,CAACD,EAAE,CAAC;IAC1D,IAAI5B,MAAM,KAAK7B,SAAS,EAAE;MACxB6B,MAAM,CAAC8B,IAAI,CAAC,CAAC;MACb,IAAI,CAAC9D,OAAO,CAAC+D,MAAM,CAACH,EAAE,CAAC;IACzB;EACF;EAEAI,0BAA0BA,CAACjD,CAAgC,EAAU;IACnE,IAAIzB,KAAK,CAAC,CAAC,EAAE;MACXJ,MAAM,CAAC+B,IAAI,CAAC,kDAAkD,CAAC;IACjE,CAAC,MAAM,IAAI5B,MAAM,CAAC,CAAC,EAAE;MACnBH,MAAM,CAAC+B,IAAI,CAAC,uDAAuD,CAAC;IACtE,CAAC,MAAM,IAAI7B,gBAAgB,CAAC,CAAC,EAAE;MAC7BF,MAAM,CAAC+B,IAAI,CACT,kEACF,CAAC;IACH,CAAC,MAAM;MACL/B,MAAM,CAAC+B,IAAI,CACT,6DACF,CAAC;IACH;IACA,OAAO,CAAC,CAAC;EACX;EAEAgD,6BAA6BA,CAAClD,CAAS,EAAQ;IAC7C;EAAA;EAGFkB,gBAAgBA,CAACZ,UAAsB,EAAEC,QAAgB,EAAa;IACpE,MAAM4C,MAAM,GACV5C,QAAQ,IAAI,CAAC,GACT;MAAE6C,cAAc,EAAE;IAAS,CAAC,GAC5B;MAAEC,SAAS,EAAE,IAAI,GAAG9C;IAAS,CAAC;IACpC,QAAQD,UAAU;MAChB,KAAKrC,UAAU,CAACsD,aAAa;QAC3B,OAAO,IAAIX,MAAM,CAAC0C,aAAa,CAACH,MAAM,CAAC;MACzC,KAAKlF,UAAU,CAAC6D,SAAS;QACvB,OAAO,IAAIlB,MAAM,CAAC2C,SAAS,CAACJ,MAAM,CAAC;MACrC,KAAKlF,UAAU,CAACuD,OAAO;QACrB,OAAO,IAAIZ,MAAM,CAAC4C,aAAa,CAACL,MAAM,CAAC;MACzC,KAAKlF,UAAU,CAAC8D,cAAc;QAC5B,OAAO,IAAInB,MAAM,CAAC6C,YAAY,CAACN,MAAM,CAAC;MACxC,KAAKlF,UAAU,CAAC+D,QAAQ;QACtB,OAAO,IAAIpB,MAAM,CAAC8C,yBAAyB,CAACP,MAAM,CAAC;IACvD;EACF;EAEAxC,aAAaA,CAACL,UAAsB,EAAU;IAC5C,QAAQA,UAAU;MAChB,KAAKrC,UAAU,CAACsD,aAAa;QAC3B,OAAO,eAAe;MACxB,KAAKtD,UAAU,CAACuD,OAAO;QACrB,OAAO,eAAe;MACxB,KAAKvD,UAAU,CAAC6D,SAAS;QACvB,OAAO,WAAW;MACpB,KAAK7D,UAAU,CAAC8D,cAAc;QAC5B,OAAO,cAAc;MACvB,KAAK9D,UAAU,CAAC+D,QAAQ;QACtB,OAAO,2BAA2B;IACtC;EACF;EAEAtB,cAAcA,CAAA,EAAG;IACf,MAAMiD,SAAS,GAAGC,SAAS,CAACD,SAAS,IAAIC,SAAS,CAACC,MAAM,IAAIjD,MAAM,CAACkD,KAAK;IACzE,IAAIH,SAAS,KAAKvE,SAAS,EAAE;MAC3B,IAAI,CAACD,QAAQ,GAAG4B,QAAQ,CAACgD,OAAO;IAClC,CAAC,MAAM,IAAI,kBAAkB,CAACC,IAAI,CAACL,SAAS,CAAC,EAAE;MAC7C,IAAI,CAACxE,QAAQ,GAAG4B,QAAQ,CAACC,OAAO;IAClC,CAAC,MAAM,IAAI,UAAU,CAACgD,IAAI,CAACL,SAAS,CAAC,EAAE;MACrC,IAAI,CAACxE,QAAQ,GAAG4B,QAAQ,CAACa,WAAW;IACtC,CAAC,MAAM;MACL,IAAI,CAACzC,QAAQ,GAAG4B,QAAQ,CAACkD,GAAG;IAC9B;EACF;EAEAC,WAAWA,CACTC,QAAgB,EAChBC,SAAiB,EACjBC,UAA4B,EAC5BC,SAA+B,EACnB;IACZ,MAAM,IAAIpG,eAAe,CAAC,+CAA+C,CAAC;EAC5E;EAEAqG,cAAcA,CAAA,EAAG;IACf,MAAM,IAAIrG,eAAe,CACvB,kDACF,CAAC;EACH;EAEAsG,sBAAsBA,CAAOC,UAA2B,EAAK;IAC3D,MAAM,IAAIvG,eAAe,CACvB,4DACF,CAAC;EACH;EAEAwG,mBAAmBA,CAACC,kBAAqC,EAAQ;IAC/D,MAAM,IAAIzG,eAAe,CACvB,uDACF,CAAC;EACH;EAEA0G,qBAAqBA,CAACT,QAAgB,EAAQ;IAC5C,MAAM,IAAIjG,eAAe,CACvB,yDACF,CAAC;EACH;AACF;;AAEA;AACA;AACA;AACA,WAAY6C,QAAQ,0BAARA,QAAQ;EAARA,QAAQ;EAARA,QAAQ;EAARA,QAAQ;EAARA,QAAQ;EAAA,OAARA,QAAQ;AAAA", "ignoreList": []}