{"version": 3, "names": ["ReanimatedError", "getShadowNodeWrapperFromRef", "checkCppVersion", "jsVersion", "isF<PERSON><PERSON>", "ReanimatedTurboModule", "WorkletsModule", "createNativeReanimatedModule", "NativeReanimatedModule", "assertSingleReanimatedInstance", "global", "_REANIMATED_VERSION_JS", "undefined", "workletsModule", "reanimatedModuleProxy", "constructor", "__DEV__", "__reanimatedModuleProxy", "installTurboModule", "DummyReanimatedModuleProxy", "scheduleOnUI", "shareable", "executeOnUIRuntimeSync", "createWorkletRuntime", "name", "initializer", "scheduleOnRuntime", "workletRuntime", "shareableWorklet", "registerSensor", "sensorType", "interval", "iosReferenceFrame", "handler", "unregisterSensor", "sensorId", "registerEventHandler", "<PERSON><PERSON><PERSON><PERSON>", "eventName", "emitterReactTag", "unregisterEventHandler", "id", "getViewProp", "viewTag", "propName", "component", "callback", "shadowNodeWrapper", "configureLayoutAnimationBatch", "layoutAnimationsBatch", "setShouldAnimateExitingForTag", "shouldAnimate", "enableLayoutAnimations", "flag", "configureProps", "uiProps", "nativeProps", "subscribeForKeyboardEvents", "isStatusBarTranslucent", "isNavigationBarTranslucent", "unsubscribeFromKeyboardEvents", "listenerId", "markNodeAsRemovable", "unmarkNodeAsRemovable"], "sourceRoot": "../../../src", "sources": ["ReanimatedModule/NativeReanimated.ts"], "mappings": "AAAA,YAAY;;AAaZ,SAASA,eAAe,QAAQ,cAAW;AAC3C,SAASC,2BAA2B,QAAQ,gBAAgB;AAC5D,SAASC,eAAe,QAAQ,yCAAsC;AACtE,SAASC,SAAS,QAAQ,mCAAgC;AAC1D,SAASC,QAAQ,QAAQ,uBAAoB;AAE7C,SAASC,qBAAqB,QAAQ,mBAAU;AAChD,SAASC,cAAc,QAAQ,sBAAa;AAG5C,OAAO,SAASC,4BAA4BA,CAAA,EAAsB;EAChE,OAAO,IAAIC,sBAAsB,CAAC,CAAC;AACrC;AAEA,SAASC,8BAA8BA,CAAA,EAAG;EACxC,IACEC,MAAM,CAACC,sBAAsB,KAAKC,SAAS,IAC3CF,MAAM,CAACC,sBAAsB,KAAKR,SAAS,EAC3C;IACA,MAAM,IAAIH,eAAe,CACvB;AACN,iKAAiKU,MAAM,CAACC,sBAAsB,cAAcR,SAAS,GACjN,CAAC;EACH;AACF;AAEA,MAAMK,sBAAsB,CAA8B;EACxD;AACF;AACA;AACA;EACE,CAACK,cAAc;EACf,CAACC,qBAAqB;EAEtBC,WAAWA,CAAA,EAAG;IACZ,IAAI,CAAC,CAACF,cAAc,GAAGP,cAAc;IACrC;IACA,IAAIU,OAAO,EAAE;MACXP,8BAA8B,CAAC,CAAC;IAClC;IACAC,MAAM,CAACC,sBAAsB,GAAGR,SAAS;IACzC,IAAIO,MAAM,CAACO,uBAAuB,KAAKL,SAAS,IAAIP,qBAAqB,EAAE;MACzE,IAAI,CAACA,qBAAqB,CAACa,kBAAkB,CAAC,CAAC,EAAE;QAC/C;QACA;QACA;QACA;QACA,IAAI,CAAC,CAACJ,qBAAqB,GAAG,IAAIK,0BAA0B,CAAC,CAAC;QAC9D;MACF;IACF;IACA,IAAIT,MAAM,CAACO,uBAAuB,KAAKL,SAAS,EAAE;MAChD,MAAM,IAAIZ,eAAe,CACvB;AACR,6JACM,CAAC;IACH;IACA,IAAIgB,OAAO,EAAE;MACXd,eAAe,CAAC,CAAC;IACnB;IACA,IAAI,CAAC,CAACY,qBAAqB,GAAGJ,MAAM,CAACO,uBAAuB;EAC9D;EAEAG,YAAYA,CAAIC,SAA0B,EAAE;IAC1C,OAAO,IAAI,CAAC,CAACP,qBAAqB,CAACM,YAAY,CAACC,SAAS,CAAC;EAC5D;EAEAC,sBAAsBA,CAAOD,SAA0B,EAAK;IAC1D,OAAO,IAAI,CAAC,CAACP,qBAAqB,CAACQ,sBAAsB,CAACD,SAAS,CAAC;EACtE;EAEAE,oBAAoBA,CAACC,IAAY,EAAEC,WAAqC,EAAE;IACxE,OAAO,IAAI,CAAC,CAACX,qBAAqB,CAACS,oBAAoB,CAACC,IAAI,EAAEC,WAAW,CAAC;EAC5E;EAEAC,iBAAiBA,CACfC,cAA8B,EAC9BC,gBAAiC,EACjC;IACA,OAAO,IAAI,CAAC,CAACd,qBAAqB,CAACY,iBAAiB,CAClDC,cAAc,EACdC,gBACF,CAAC;EACH;EAEAC,cAAcA,CACZC,UAAkB,EAClBC,QAAgB,EAChBC,iBAAyB,EACzBC,OAA8D,EAC9D;IACA,OAAO,IAAI,CAAC,CAACnB,qBAAqB,CAACe,cAAc,CAC/CC,UAAU,EACVC,QAAQ,EACRC,iBAAiB,EACjBC,OACF,CAAC;EACH;EAEAC,gBAAgBA,CAACC,QAAgB,EAAE;IACjC,OAAO,IAAI,CAAC,CAACrB,qBAAqB,CAACoB,gBAAgB,CAACC,QAAQ,CAAC;EAC/D;EAEAC,oBAAoBA,CAClBC,YAA6B,EAC7BC,SAAiB,EACjBC,eAAuB,EACvB;IACA,OAAO,IAAI,CAAC,CAACzB,qBAAqB,CAACsB,oBAAoB,CACrDC,YAAY,EACZC,SAAS,EACTC,eACF,CAAC;EACH;EAEAC,sBAAsBA,CAACC,EAAU,EAAE;IACjC,OAAO,IAAI,CAAC,CAAC3B,qBAAqB,CAAC0B,sBAAsB,CAACC,EAAE,CAAC;EAC/D;EAEAC,WAAWA,CACTC,OAAe,EACfC,QAAgB,EAChBC,SAAsC;EAAE;EACxCC,QAA8B,EAC9B;IACA,IAAIC,iBAAiB;IACrB,IAAI3C,QAAQ,CAAC,CAAC,EAAE;MACd2C,iBAAiB,GAAG9C,2BAA2B,CAC7C4C,SACF,CAAC;MACD,OAAO,IAAI,CAAC,CAAC/B,qBAAqB,CAAC4B,WAAW,CAC5CK,iBAAiB,EACjBH,QAAQ,EACRE,QACF,CAAC;IACH;IAEA,OAAO,IAAI,CAAC,CAAChC,qBAAqB,CAAC4B,WAAW,CAACC,OAAO,EAAEC,QAAQ,EAAEE,QAAQ,CAAC;EAC7E;EAEAE,6BAA6BA,CAC3BC,qBAAiD,EACjD;IACA,IAAI,CAAC,CAACnC,qBAAqB,CAACkC,6BAA6B,CACvDC,qBACF,CAAC;EACH;EAEAC,6BAA6BA,CAACP,OAAe,EAAEQ,aAAsB,EAAE;IACrE,IAAI,CAAC,CAACrC,qBAAqB,CAACoC,6BAA6B,CACvDP,OAAO,EACPQ,aACF,CAAC;EACH;EAEAC,sBAAsBA,CAACC,IAAa,EAAE;IACpC,IAAI,CAAC,CAACvC,qBAAqB,CAACsC,sBAAsB,CAACC,IAAI,CAAC;EAC1D;EAEAC,cAAcA,CAACC,OAAiB,EAAEC,WAAqB,EAAE;IACvD,IAAI,CAAC,CAAC1C,qBAAqB,CAACwC,cAAc,CAACC,OAAO,EAAEC,WAAW,CAAC;EAClE;EAEAC,0BAA0BA,CACxBxB,OAAsC,EACtCyB,sBAA+B,EAC/BC,0BAAmC,EACnC;IACA,OAAO,IAAI,CAAC,CAAC7C,qBAAqB,CAAC2C,0BAA0B,CAC3DxB,OAAO,EACPyB,sBAAsB,EACtBC,0BACF,CAAC;EACH;EAEAC,6BAA6BA,CAACC,UAAkB,EAAE;IAChD,IAAI,CAAC,CAAC/C,qBAAqB,CAAC8C,6BAA6B,CAACC,UAAU,CAAC;EACvE;EAEAC,mBAAmBA,CAACf,iBAAoC,EAAE;IACxD,IAAI,CAAC,CAACjC,qBAAqB,CAACgD,mBAAmB,CAACf,iBAAiB,CAAC;EACpE;EAEAgB,qBAAqBA,CAACpB,OAAe,EAAE;IACrC,IAAI,CAAC,CAAC7B,qBAAqB,CAACiD,qBAAqB,CAACpB,OAAO,CAAC;EAC5D;AACF;AAEA,MAAMxB,0BAA0B,CAAkC;EAChEC,YAAYA,CAAA,EAAS,CAAC;EACtBE,sBAAsBA,CAAA,EAAG;IACvB,OAAO,IAAI;EACb;EAEAC,oBAAoBA,CAAA,EAAG;IACrB,OAAO,IAAI;EACb;EAEAG,iBAAiBA,CAAA,EAAS,CAAC;EAC3BsB,6BAA6BA,CAAA,EAAS,CAAC;EACvCE,6BAA6BA,CAAA,EAAS,CAAC;EACvCE,sBAAsBA,CAAA,EAAS,CAAC;EAChCE,cAAcA,CAAA,EAAS,CAAC;EACxBG,0BAA0BA,CAAA,EAAW;IACnC,OAAO,CAAC,CAAC;EACX;EAEAG,6BAA6BA,CAAA,EAAS,CAAC;EACvCE,mBAAmBA,CAAA,EAAS,CAAC;EAC7BC,qBAAqBA,CAAA,EAAS,CAAC;EAE/BlC,cAAcA,CAAA,EAAW;IACvB,OAAO,CAAC,CAAC;EACX;EAEAK,gBAAgBA,CAAA,EAAS,CAAC;EAC1BE,oBAAoBA,CAAA,EAAW;IAC7B,OAAO,CAAC,CAAC;EACX;EAEAI,sBAAsBA,CAAA,EAAS,CAAC;EAChCE,WAAWA,CAAA,EAAG;IACZ,OAAO,IAAI;EACb;AACF", "ignoreList": []}