{"version": 3, "names": ["shouldBeUseWeb", "createJSReanimatedModule", "createNativeReanimatedModule", "ReanimatedModule"], "sourceRoot": "../../../src", "sources": ["ReanimatedModule/reanimatedModuleInstance.ts"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,cAAc,QAAQ,uBAAoB;AACnD,SAASC,wBAAwB,QAAQ,0BAAiB;AAC1D,SAASC,4BAA4B,QAAQ,uBAAoB;AAEjE,OAAO,MAAMC,gBAAgB,GAAGH,cAAc,CAAC,CAAC,GAC5CC,wBAAwB,CAAC,CAAC,GAC1BC,4BAA4B,CAAC,CAAC", "ignoreList": []}