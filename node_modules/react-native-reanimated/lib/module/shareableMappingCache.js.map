{"version": 3, "names": ["shouldBeUseWeb", "SHOULD_BE_USE_WEB", "shareableMappingFlag", "Symbol", "cache", "WeakMap", "shareableMappingCache", "set", "get", "shareable", "shareableRef", "bind"], "sourceRoot": "../../src", "sources": ["shareableMappingCache.ts"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,cAAc,QAAQ,sBAAmB;AAElD,MAAMC,iBAAiB,GAAGD,cAAc,CAAC,CAAC;;AAE1C;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAME,oBAAoB,GAAGC,MAAM,CAAC,gBAAgB,CAAC;;AAE5D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,MAAMC,KAAK,GAAGH,iBAAiB,GAC3B,IAAI,GACJ,IAAII,OAAO,CAAgC,CAAC;AAEhD,OAAO,MAAMC,qBAAqB,GAAGL,iBAAiB,GAClD;EACEM,GAAGA,CAAA,EAAG;IACJ;EAAA,CACD;EACDC,GAAGA,CAAA,EAAG;IACJ,OAAO,IAAI;EACb;AACF,CAAC,GACD;EACED,GAAGA,CAACE,SAAiB,EAAEC,YAA2B,EAAQ;IACxDN,KAAK,CAAEG,GAAG,CAACE,SAAS,EAAEC,YAAY,IAAIR,oBAAoB,CAAC;EAC7D,CAAC;EACDM,GAAG,EAAEJ,KAAK,CAAEI,GAAG,CAACG,IAAI,CAACP,KAAK;AAC5B,CAAC", "ignoreList": []}