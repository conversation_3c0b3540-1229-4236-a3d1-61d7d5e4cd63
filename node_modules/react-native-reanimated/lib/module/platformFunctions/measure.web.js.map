{"version": 3, "names": ["logger", "measure", "animatedRef", "element", "warn", "viewportOffset", "getBoundingClientRect", "width", "offsetWidth", "height", "offsetHeight", "x", "offsetLeft", "y", "offsetTop", "pageX", "left", "pageY", "top"], "sourceRoot": "../../../src", "sources": ["platformFunctions/measure.web.ts"], "mappings": "AAAA,YAAY;;AAKZ,SAASA,MAAM,QAAQ,oBAAW;AAElC,OAAO,SAASC,OAAOA,CACrBC,WAA2B,EACA;EAC3B,MAAMC,OAAO,GAAGD,WAAW,CAAC,CAAqB;EAEjD,IAAIC,OAAO,KAAK,CAAC,CAAC,EAAE;IAClBH,MAAM,CAACI,IAAI,CACT,qBAAqBD,OAAO,4JAC9B,CAAC;IACD,OAAO,IAAI;EACb;EAEA,MAAME,cAAc,GAAGF,OAAO,CAACG,qBAAqB,CAAC,CAAC;EACtD,OAAO;IACLC,KAAK,EAAEJ,OAAO,CAACK,WAAW;IAC1BC,MAAM,EAAEN,OAAO,CAACO,YAAY;IAC5BC,CAAC,EAAER,OAAO,CAACS,UAAU;IACrBC,CAAC,EAAEV,OAAO,CAACW,SAAS;IACpBC,KAAK,EAAEV,cAAc,CAACW,IAAI;IAC1BC,KAAK,EAAEZ,cAAc,CAACa;EACxB,CAAC;AACH", "ignoreList": []}