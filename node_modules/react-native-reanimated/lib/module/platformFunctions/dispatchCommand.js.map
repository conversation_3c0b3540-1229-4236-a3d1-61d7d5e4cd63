{"version": 3, "names": ["logger", "isChromeDebugger", "isF<PERSON><PERSON>", "isJest", "shouldBeUseWeb", "dispatchCommand", "dispatchCommandFabric", "animatedRef", "commandName", "args", "_WORKLET", "shadowNodeWrapper", "global", "_dispatchCommandFabric", "dispatchCommandPaper", "viewTag", "_dispatchCommandPaper", "dispatchCommandJest", "warn", "dispatchCommandChromeDebugger", "dispatchCommandDefault"], "sourceRoot": "../../../src", "sources": ["platformFunctions/dispatchCommand.ts"], "mappings": "AAAA,YAAY;;AASZ,SAASA,MAAM,QAAQ,oBAAW;AAClC,SACEC,gBAAgB,EAChBC,QAAQ,EACRC,MAAM,EACNC,cAAc,QACT,uBAAoB;AAQ3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIC,eAAgC;AAE3C,SAASC,qBAAqBA,CAC5BC,WAA8C,EAC9CC,WAAmB,EACnBC,IAAoB,GAAG,EAAE,EACzB;EACA,SAAS;;EACT,IAAI,CAACC,QAAQ,EAAE;IACb;EACF;EAEA,MAAMC,iBAAiB,GAAGJ,WAAW,CAAC,CAAsB;EAC5DK,MAAM,CAACC,sBAAsB,CAAEF,iBAAiB,EAAEH,WAAW,EAAEC,IAAI,CAAC;AACtE;AAEA,SAASK,oBAAoBA,CAC3BP,WAA8C,EAC9CC,WAAmB,EACnBC,IAAoB,GAAG,EAAE,EACzB;EACA,SAAS;;EACT,IAAI,CAACC,QAAQ,EAAE;IACb;EACF;EAEA,MAAMK,OAAO,GAAGR,WAAW,CAAC,CAAW;EACvCK,MAAM,CAACI,qBAAqB,CAAED,OAAO,EAAEP,WAAW,EAAEC,IAAI,CAAC;AAC3D;AAEA,SAASQ,mBAAmBA,CAAA,EAAG;EAC7BjB,MAAM,CAACkB,IAAI,CAAC,+CAA+C,CAAC;AAC9D;AAEA,SAASC,6BAA6BA,CAAA,EAAG;EACvCnB,MAAM,CAACkB,IAAI,CAAC,0DAA0D,CAAC;AACzE;AAEA,SAASE,sBAAsBA,CAAA,EAAG;EAChCpB,MAAM,CAACkB,IAAI,CAAC,2DAA2D,CAAC;AAC1E;AAEA,IAAI,CAACd,cAAc,CAAC,CAAC,EAAE;EACrB;EACA;EACA;EACA,IAAIF,QAAQ,CAAC,CAAC,EAAE;IACdG,eAAe,GAAGC,qBAAmD;EACvE,CAAC,MAAM;IACLD,eAAe,GAAGS,oBAAkD;EACtE;AACF,CAAC,MAAM,IAAIX,MAAM,CAAC,CAAC,EAAE;EACnBE,eAAe,GAAGY,mBAAmB;AACvC,CAAC,MAAM,IAAIhB,gBAAgB,CAAC,CAAC,EAAE;EAC7BI,eAAe,GAAGc,6BAA6B;AACjD,CAAC,MAAM;EACLd,eAAe,GAAGe,sBAAsB;AAC1C", "ignoreList": []}