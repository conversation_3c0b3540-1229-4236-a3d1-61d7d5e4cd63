{"version": 3, "names": ["logger", "isChromeDebugger", "isF<PERSON><PERSON>", "isJest", "shouldBeUseWeb", "measure", "measureFabric", "animatedRef", "_WORKLET", "viewTag", "warn", "measured", "global", "_measureFabric", "x", "isNaN", "measurePaper", "_measurePaper", "measureJest", "measureChromeDebugger", "measureDefault"], "sourceRoot": "../../../src", "sources": ["platformFunctions/measure.ts"], "mappings": "AAAA,YAAY;;AASZ,SAASA,MAAM,QAAQ,oBAAW;AAClC,SACEC,gBAAgB,EAChBC,QAAQ,EACRC,MAAM,EACNC,cAAc,QACT,uBAAoB;AAM3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIC,OAAgB;AAE3B,SAASC,aAAaA,CAACC,WAA8C,EAAE;EACrE,SAAS;;EACT,IAAI,CAACC,QAAQ,EAAE;IACb,OAAO,IAAI;EACb;EAEA,MAAMC,OAAO,GAAGF,WAAW,CAAC,CAAC;EAC7B,IAAIE,OAAO,KAAK,CAAC,CAAC,EAAE;IAClBT,MAAM,CAACU,IAAI,CACT,qBAAqBD,OAAO,4JAC9B,CAAC;IACD,OAAO,IAAI;EACb;EAEA,MAAME,QAAQ,GAAGC,MAAM,CAACC,cAAc,CAAEJ,OAA4B,CAAC;EACrE,IAAIE,QAAQ,KAAK,IAAI,EAAE;IACrBX,MAAM,CAACU,IAAI,CACT,kNACF,CAAC;IACD,OAAO,IAAI;EACb,CAAC,MAAM,IAAIC,QAAQ,CAACG,CAAC,KAAK,CAAC,OAAO,EAAE;IAClCd,MAAM,CAACU,IAAI,CACT,qGACF,CAAC;IACD,OAAO,IAAI;EACb,CAAC,MAAM,IAAIK,KAAK,CAACJ,QAAQ,CAACG,CAAC,CAAC,EAAE;IAC5Bd,MAAM,CAACU,IAAI,CACT,qHACF,CAAC;IACD,OAAO,IAAI;EACb,CAAC,MAAM;IACL,OAAOC,QAAQ;EACjB;AACF;AAEA,SAASK,YAAYA,CAACT,WAA8C,EAAE;EACpE,SAAS;;EACT,IAAI,CAACC,QAAQ,EAAE;IACb,OAAO,IAAI;EACb;EAEA,MAAMC,OAAO,GAAGF,WAAW,CAAC,CAAC;EAC7B,IAAIE,OAAO,KAAK,CAAC,CAAC,EAAE;IAClBT,MAAM,CAACU,IAAI,CACT,qBAAqBD,OAAO,4JAC9B,CAAC;IACD,OAAO,IAAI;EACb;EAEA,MAAME,QAAQ,GAAGC,MAAM,CAACK,aAAa,CAAER,OAAiB,CAAC;EACzD,IAAIE,QAAQ,KAAK,IAAI,EAAE;IACrBX,MAAM,CAACU,IAAI,CACT,qBACED,OAAO,0MAEX,CAAC;IACD,OAAO,IAAI;EACb,CAAC,MAAM,IAAIE,QAAQ,CAACG,CAAC,KAAK,CAAC,OAAO,EAAE;IAClCd,MAAM,CAACU,IAAI,CACT,qBACED,OAAO,6FAEX,CAAC;IACD,OAAO,IAAI;EACb,CAAC,MAAM,IAAIM,KAAK,CAACJ,QAAQ,CAACG,CAAC,CAAC,EAAE;IAC5Bd,MAAM,CAACU,IAAI,CACT,qBACED,OAAO,6GAEX,CAAC;IACD,OAAO,IAAI;EACb,CAAC,MAAM;IACL,OAAOE,QAAQ;EACjB;AACF;AAEA,SAASO,WAAWA,CAAA,EAAG;EACrBlB,MAAM,CAACU,IAAI,CAAC,qCAAqC,CAAC;EAClD,OAAO,IAAI;AACb;AAEA,SAASS,qBAAqBA,CAAA,EAAG;EAC/BnB,MAAM,CAACU,IAAI,CAAC,gDAAgD,CAAC;EAC7D,OAAO,IAAI;AACb;AAEA,SAASU,cAAcA,CAAA,EAAG;EACxBpB,MAAM,CAACU,IAAI,CAAC,mDAAmD,CAAC;EAChE,OAAO,IAAI;AACb;AAEA,IAAI,CAACN,cAAc,CAAC,CAAC,EAAE;EACrB;EACA;EACA;EACA,IAAIF,QAAQ,CAAC,CAAC,EAAE;IACdG,OAAO,GAAGC,aAAmC;EAC/C,CAAC,MAAM;IACLD,OAAO,GAAGW,YAAkC;EAC9C;AACF,CAAC,MAAM,IAAIb,MAAM,CAAC,CAAC,EAAE;EACnBE,OAAO,GAAGa,WAAW;AACvB,CAAC,MAAM,IAAIjB,gBAAgB,CAAC,CAAC,EAAE;EAC7BI,OAAO,GAAGc,qBAAqB;AACjC,CAAC,MAAM;EACLd,OAAO,GAAGe,cAAc;AAC1B", "ignoreList": []}