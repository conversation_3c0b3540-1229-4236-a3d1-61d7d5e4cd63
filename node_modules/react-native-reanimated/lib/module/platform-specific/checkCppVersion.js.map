{"version": 3, "names": ["ReanimatedError", "logger", "jsVersion", "checkCppVersion", "cppVersion", "global", "_REANIMATED_VERSION_CPP", "undefined", "warn", "ok", "matchVersion", "version1", "version2", "match", "major1", "minor1", "split", "major2", "minor2"], "sourceRoot": "../../../src", "sources": ["platform-specific/checkCppVersion.ts"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,eAAe,QAAQ,cAAW;AAC3C,SAASC,MAAM,QAAQ,oBAAW;AAClC,SAASC,SAAS,QAAQ,gBAAa;AAEvC,OAAO,SAASC,eAAeA,CAAA,EAAG;EAChC,MAAMC,UAAU,GAAGC,MAAM,CAACC,uBAAuB;EACjD,IAAIF,UAAU,KAAKG,SAAS,EAAE;IAC5BN,MAAM,CAACO,IAAI,CACT;AACN,4KACI,CAAC;IACD;EACF;EACA,MAAMC,EAAE,GAAGC,YAAY,CAACR,SAAS,EAAEE,UAAU,CAAC;EAC9C,IAAI,CAACK,EAAE,EAAE;IACP,MAAM,IAAIT,eAAe,CACvB,mEAAmEE,SAAS,OAAOE,UAAU;AACnG,4KACI,CAAC;EACH;AACF;;AAEA;AACA;AACA,OAAO,SAASM,YAAYA,CAACC,QAAgB,EAAEC,QAAgB,EAAE;EAC/D,IAAID,QAAQ,CAACE,KAAK,CAAC,iBAAiB,CAAC,IAAID,QAAQ,CAACC,KAAK,CAAC,iBAAiB,CAAC,EAAE;IAC1E;IACA,MAAM,CAACC,MAAM,EAAEC,MAAM,CAAC,GAAGJ,QAAQ,CAACK,KAAK,CAAC,GAAG,CAAC;IAC5C,MAAM,CAACC,MAAM,EAAEC,MAAM,CAAC,GAAGN,QAAQ,CAACI,KAAK,CAAC,GAAG,CAAC;IAC5C,OAAOF,MAAM,KAAKG,MAAM,IAAIF,MAAM,KAAKG,MAAM;EAC/C,CAAC,MAAM;IACL;IACA,OAAOP,QAAQ,KAAKC,QAAQ;EAC9B;AACF", "ignoreList": []}