{"version": 3, "names": ["configureProps", "applyStyle", "getSwipeSimulator", "startScreenTransition", "screenTransitionConfig", "stackTag", "sharedEvent", "addListener", "value", "getLockAxis", "goBackGesture", "includes", "undefined", "finishScreenTransition", "removeListener", "lockAxis", "step"], "sourceRoot": "../../../src", "sources": ["screenTransition/animationManager.ts"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,cAAc,QAAQ,oBAAiB;AAEhD,SAASC,UAAU,QAAQ,mBAAgB;AAC3C,SAASC,iBAAiB,QAAQ,qBAAkB;AAEpDF,cAAc,CAAC,CAAC;AAEhB,OAAO,SAASG,qBAAqBA,CACnCC,sBAA8C,EAC9C;EACA,SAAS;;EACT,MAAM;IAAEC,QAAQ;IAAEC;EAAY,CAAC,GAAGF,sBAAsB;EACxDE,WAAW,CAACC,WAAW,CAACF,QAAQ,EAAE,MAAM;IACtCJ,UAAU,CAACG,sBAAsB,EAAEE,WAAW,CAACE,KAAK,CAAC;EACvD,CAAC,CAAC;AACJ;AAEA,SAASC,WAAWA,CAACC,aAAqB,EAAY;EACpD,SAAS;;EACT,IAAI,CAAC,YAAY,EAAE,WAAW,EAAE,iBAAiB,CAAC,CAACC,QAAQ,CAACD,aAAa,CAAC,EAAE;IAC1E,OAAO,GAAG;EACZ,CAAC,MAAM,IACL,CAAC,SAAS,EAAE,WAAW,EAAE,eAAe,CAAC,CAACC,QAAQ,CAACD,aAAa,CAAC,EACjE;IACA,OAAO,GAAG;EACZ;EACA,OAAOE,SAAS;AAClB;AAEA,OAAO,SAASC,sBAAsBA,CACpCT,sBAA8C,EAC9C;EACA,SAAS;;EACT,MAAM;IAAEC,QAAQ;IAAEC,WAAW;IAAEI;EAAc,CAAC,GAAGN,sBAAsB;EACvEE,WAAW,CAACQ,cAAc,CAACT,QAAQ,CAAC;EACpC,MAAMU,QAAQ,GAAGN,WAAW,CAACC,aAAa,CAAC;EAC3C,MAAMM,IAAI,GAAGd,iBAAiB,CAC5BI,WAAW,CAACE,KAAK,EACjBJ,sBAAsB,EACtBW,QACF,CAAC;EACDC,IAAI,CAAC,CAAC;AACR", "ignoreList": []}