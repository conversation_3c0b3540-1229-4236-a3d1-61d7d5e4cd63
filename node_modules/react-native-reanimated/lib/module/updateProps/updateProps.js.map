{"version": 3, "names": ["processColorsInProps", "ReanimatedError", "isF<PERSON><PERSON>", "isJest", "shouldBeUseWeb", "_updatePropsJS", "runOnUIImmediately", "processTransformOrigin", "updateProps", "viewDescriptors", "updates", "isAnimatedProps", "value", "for<PERSON>ach", "viewDescriptor", "component", "tag", "transform<PERSON><PERSON>in", "global", "UpdatePropsManager", "update", "updatePropsJestWrapper", "animatedValues", "adapters", "adapter", "current", "createUpdatePropsManager", "operations", "push", "shadowNodeWrapper", "length", "queueMicrotask", "flush", "_updatePropsFabric", "name", "_updatePropsPaper", "maybeThrowError", "Proxy", "get", "set"], "sourceRoot": "../../../src", "sources": ["updateProps/updateProps.ts"], "mappings": "AAAA;AACA,YAAY;;AAIZ,SAASA,oBAAoB,QAAQ,cAAW;AAMhD,SAASC,eAAe,QAAQ,cAAW;AAE3C,SAASC,QAAQ,EAAEC,MAAM,EAAEC,cAAc,QAAQ,uBAAoB;AAErE,SAASC,cAAc,QAAQ,4CAAmC;AAClE,SAASC,kBAAkB,QAAQ,eAAY;AAC/C,SAASC,sBAAsB,QAAQ,6BAA0B;AAEjE,IAAIC,WAIK;AAET,IAAIJ,cAAc,CAAC,CAAC,EAAE;EACpBI,WAAW,GAAGA,CAACC,eAAe,EAAEC,OAAO,EAAEC,eAAe,KAAK;IAC3D,SAAS;;IACTF,eAAe,CAACG,KAAK,EAAEC,OAAO,CAAEC,cAAc,IAAK;MACjD,MAAMC,SAAS,GAAGD,cAAc,CAACE,GAA4B;MAC7DX,cAAc,CAACK,OAAO,EAAEK,SAAS,EAAEJ,eAAe,CAAC;IACrD,CAAC,CAAC;EACJ,CAAC;AACH,CAAC,MAAM;EACLH,WAAW,GAAGA,CAACC,eAAe,EAAEC,OAAO,KAAK;IAC1C,SAAS;;IACTV,oBAAoB,CAACU,OAAO,CAAC;IAC7B,IAAI,iBAAiB,IAAIA,OAAO,EAAE;MAChCA,OAAO,CAACO,eAAe,GAAGV,sBAAsB,CAACG,OAAO,CAACO,eAAe,CAAC;IAC3E;IACAC,MAAM,CAACC,kBAAkB,CAACC,MAAM,CAACX,eAAe,EAAEC,OAAO,CAAC;EAC5D,CAAC;AACH;AAEA,OAAO,MAAMW,sBAAsB,GAAGA,CACpCZ,eAAuC,EACvCC,OAA2B,EAC3BY,cAAoD,EACpDC,QAAmD,KAC1C;EACTA,QAAQ,CAACV,OAAO,CAAEW,OAAO,IAAK;IAC5BA,OAAO,CAACd,OAAO,CAAC;EAClB,CAAC,CAAC;EACFY,cAAc,CAACG,OAAO,CAACb,KAAK,GAAG;IAC7B,GAAGU,cAAc,CAACG,OAAO,CAACb,KAAK;IAC/B,GAAGF;EACL,CAAC;EAEDF,WAAW,CAACC,eAAe,EAAEC,OAAO,CAAC;AACvC,CAAC;AAED,eAAeF,WAAW;AAE1B,MAAMkB,wBAAwB,GAAGxB,QAAQ,CAAC,CAAC,GACvC,MAAM;EACJ,SAAS;;EACT;EACA,MAAMyB,UAGH,GAAG,EAAE;EACR,OAAO;IACLP,MAAMA,CACJX,eAAuC,EACvCC,OAAwC,EACxC;MACAD,eAAe,CAACG,KAAK,CAACC,OAAO,CAAEC,cAAc,IAAK;QAChDa,UAAU,CAACC,IAAI,CAAC;UACdC,iBAAiB,EAAEf,cAAc,CAACe,iBAAiB;UACnDnB;QACF,CAAC,CAAC;QACF,IAAIiB,UAAU,CAACG,MAAM,KAAK,CAAC,EAAE;UAC3BC,cAAc,CAAC,IAAI,CAACC,KAAK,CAAC;QAC5B;MACF,CAAC,CAAC;IACJ,CAAC;IACDA,KAAKA,CAAA,EAAa;MAChBd,MAAM,CAACe,kBAAkB,CAAEN,UAAU,CAAC;MACtCA,UAAU,CAACG,MAAM,GAAG,CAAC;IACvB;EACF,CAAC;AACH,CAAC,GACD,MAAM;EACJ,SAAS;;EACT;EACA,MAAMH,UAIH,GAAG,EAAE;EACR,OAAO;IACLP,MAAMA,CACJX,eAAuC,EACvCC,OAAwC,EACxC;MACAD,eAAe,CAACG,KAAK,CAACC,OAAO,CAAEC,cAAc,IAAK;QAChDa,UAAU,CAACC,IAAI,CAAC;UACdZ,GAAG,EAAEF,cAAc,CAACE,GAAa;UACjCkB,IAAI,EAAEpB,cAAc,CAACoB,IAAI,IAAI,SAAS;UACtCxB;QACF,CAAC,CAAC;QACF,IAAIiB,UAAU,CAACG,MAAM,KAAK,CAAC,EAAE;UAC3BC,cAAc,CAAC,IAAI,CAACC,KAAK,CAAC;QAC5B;MACF,CAAC,CAAC;IACJ,CAAC;IACDA,KAAKA,CAAA,EAAa;MAChBd,MAAM,CAACiB,iBAAiB,CAAER,UAAU,CAAC;MACrCA,UAAU,CAACG,MAAM,GAAG,CAAC;IACvB;EACF,CAAC;AACH,CAAC;AAEL,IAAI1B,cAAc,CAAC,CAAC,EAAE;EACpB,MAAMgC,eAAe,GAAGA,CAAA,KAAM;IAC5B;IACA;IACA,IAAI,CAACjC,MAAM,CAAC,CAAC,EAAE;MACb,MAAM,IAAIF,eAAe,CACvB,+DACF,CAAC;IACH;EACF,CAAC;EACDiB,MAAM,CAACC,kBAAkB,GAAG,IAAIkB,KAAK,CACnC,CAAC,CAAC,EACF;IACEC,GAAG,EAAEF,eAAe;IACpBG,GAAG,EAAEA,CAAA,KAAM;MACTH,eAAe,CAAC,CAAC;MACjB,OAAO,KAAK;IACd;EACF,CACF,CAAC;AACH,CAAC,MAAM;EACL9B,kBAAkB,CAAC,MAAM;IACvB,SAAS;;IACTY,MAAM,CAACC,kBAAkB,GAAGO,wBAAwB,CAAC,CAAC;EACxD,CAAC,CAAC,CAAC,CAAC;AACN;;AAEA;AACA;AACA;AACA", "ignoreList": []}