{"version": 3, "names": ["with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "advanceAnimationByTime", "advanceAnimationByFrame", "setUpTests", "getAnimatedStyle"], "sourceRoot": "../../src", "sources": ["jestUtils.web.ts"], "mappings": "AAAA,YAAY;;AACZ;AACA;AACA;AAEA,OAAO,SAASA,mBAAmBA,CAAA,EAAG;EACpC;AAAA;AAGF,OAAO,SAASC,sBAAsBA,CAAA,EAAG;EACvC;AAAA;AAGF,OAAO,SAASC,uBAAuBA,CAAA,EAAG;EACxC;AAAA;AAGF,OAAO,SAASC,UAAUA,CAAA,EAAG;EAC3B;AAAA;AAGF,OAAO,SAASC,gBAAgBA,CAAA,EAAG;EACjC;AAAA", "ignoreList": []}