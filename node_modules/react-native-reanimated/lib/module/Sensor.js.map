{"version": 3, "names": ["SensorType", "makeMutable", "ReanimatedModule", "initSensorData", "sensorType", "ROTATION", "qw", "qx", "qy", "qz", "yaw", "pitch", "roll", "interfaceOrientation", "x", "y", "z", "Sensor", "listenersNumber", "sensorId", "constructor", "config", "data", "register", "<PERSON><PERSON><PERSON><PERSON>", "registerSensor", "interval", "iosReferenceFrame", "isRunning", "isAvailable", "getSharedValue", "unregister", "unregisterSensor"], "sourceRoot": "../../src", "sources": ["Sensor.ts"], "mappings": "AAAA,YAAY;;AASZ,SAASA,UAAU,QAAQ,kBAAe;AAC1C,SAASC,WAAW,QAAQ,eAAY;AACxC,SAASC,gBAAgB,QAAQ,oBAAoB;AAErD,SAASC,cAAcA,CACrBC,UAAsB,EACgB;EACtC,IAAIA,UAAU,KAAKJ,UAAU,CAACK,QAAQ,EAAE;IACtC,OAAOJ,WAAW,CAA0B;MAC1CK,EAAE,EAAE,CAAC;MACLC,EAAE,EAAE,CAAC;MACLC,EAAE,EAAE,CAAC;MACLC,EAAE,EAAE,CAAC;MACLC,GAAG,EAAE,CAAC;MACNC,KAAK,EAAE,CAAC;MACRC,IAAI,EAAE,CAAC;MACPC,oBAAoB,EAAE;IACxB,CAAC,CAAC;EACJ,CAAC,MAAM;IACL,OAAOZ,WAAW,CAA0B;MAC1Ca,CAAC,EAAE,CAAC;MACJC,CAAC,EAAE,CAAC;MACJC,CAAC,EAAE,CAAC;MACJH,oBAAoB,EAAE;IACxB,CAAC,CAAC;EACJ;AACF;AAEA,eAAe,MAAMI,MAAM,CAAC;EACnBC,eAAe,GAAG,CAAC;EAClBC,QAAQ,GAAkB,IAAI;EAKtCC,WAAWA,CAAChB,UAAsB,EAAEiB,MAAoB,EAAE;IACxD,IAAI,CAACjB,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACiB,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,IAAI,GAAGnB,cAAc,CAACC,UAAU,CAAC;EACxC;EAEAmB,QAAQA,CACNC,YAAmE,EACnE;IACA,MAAMH,MAAM,GAAG,IAAI,CAACA,MAAM;IAC1B,MAAMjB,UAAU,GAAG,IAAI,CAACA,UAAU;IAClC,IAAI,CAACe,QAAQ,GAAGjB,gBAAgB,CAACuB,cAAc,CAC7CrB,UAAU,EACViB,MAAM,CAACK,QAAQ,KAAK,MAAM,GAAG,CAAC,CAAC,GAAGL,MAAM,CAACK,QAAQ,EACjDL,MAAM,CAACM,iBAAiB,EACxBH,YACF,CAAC;IACD,OAAO,IAAI,CAACL,QAAQ,KAAK,CAAC,CAAC;EAC7B;EAEAS,SAASA,CAAA,EAAG;IACV,OAAO,IAAI,CAACT,QAAQ,KAAK,CAAC,CAAC,IAAI,IAAI,CAACA,QAAQ,KAAK,IAAI;EACvD;EAEAU,WAAWA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACV,QAAQ,KAAK,CAAC,CAAC;EAC7B;EAEAW,cAAcA,CAAA,EAAG;IACf,OAAO,IAAI,CAACR,IAAI;EAClB;EAEAS,UAAUA,CAAA,EAAG;IACX,IAAI,IAAI,CAACZ,QAAQ,KAAK,IAAI,IAAI,IAAI,CAACA,QAAQ,KAAK,CAAC,CAAC,EAAE;MAClDjB,gBAAgB,CAAC8B,gBAAgB,CAAC,IAAI,CAACb,QAAQ,CAAC;IAClD;IACA,IAAI,CAACA,QAAQ,GAAG,IAAI;EACtB;AACF", "ignoreList": []}