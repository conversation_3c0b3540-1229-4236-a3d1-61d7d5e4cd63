{"version": 3, "names": ["isWorkletFunction", "ReanimatedError", "registerReanimatedError", "setupCallGuard", "setupConsole", "registerLoggerConfig", "shouldBeUseWeb", "ReanimatedModule", "makeShareableCloneOnUIRecursive", "makeShareableCloneRecursive", "SHOULD_BE_USE_WEB", "createWorkletRuntime", "name", "initializer", "config", "__reanimatedLoggerConfig", "runOnRuntime", "workletRuntime", "worklet", "__DEV__", "_WORKLET", "args", "global", "_scheduleOnRuntime", "scheduleOnRuntime"], "sourceRoot": "../../src", "sources": ["runtimes.ts"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,iBAAiB,QAAQ,kBAAe;AACjD,SAASC,eAAe,EAAEC,uBAAuB,QAAQ,aAAU;AACnE,SAASC,cAAc,EAAEC,YAAY,QAAQ,mBAAgB;AAC7D,SAASC,oBAAoB,QAAQ,mBAAU;AAC/C,SAASC,cAAc,QAAQ,sBAAmB;AAClD,SAASC,gBAAgB,QAAQ,oBAAoB;AACrD,SACEC,+BAA+B,EAC/BC,2BAA2B,QACtB,iBAAc;AAErB,MAAMC,iBAAiB,GAAGJ,cAAc,CAAC,CAAC;;AAO1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAMA,OAAO,SAASK,oBAAoBA,CAClCC,IAAY,EACZC,WAAuC,EACvB;EAChB;EACA;EACA,MAAMC,MAAM,GAAGC,wBAAwB;EACvC,OAAOR,gBAAgB,CAACI,oBAAoB,CAC1CC,IAAI,EACJH,2BAA2B,CAAC,MAAM;IAChC,SAAS;;IACTP,uBAAuB,CAAC,CAAC;IACzBG,oBAAoB,CAACS,MAAM,CAAC;IAC5BX,cAAc,CAAC,CAAC;IAChBC,YAAY,CAAC,CAAC;IACdS,WAAW,GAAG,CAAC;EACjB,CAAC,CACH,CAAC;AACH;;AAEA;;AAKA;AACA,OAAO,SAASG,YAAYA,CAC1BC,cAA8B,EAC9BC,OAA2C,EAClB;EACzB,SAAS;;EACT,IAAIC,OAAO,IAAI,CAACT,iBAAiB,IAAI,CAACV,iBAAiB,CAACkB,OAAO,CAAC,EAAE;IAChE,MAAM,IAAIjB,eAAe,CACvB,yDAAyD,IACtDmB,QAAQ,GACL,8FAA8F,GAC9F,EAAE,CACV,CAAC;EACH;EACA,IAAIA,QAAQ,EAAE;IACZ,OAAO,CAAC,GAAGC,IAAI,KACbC,MAAM,CAACC,kBAAkB,CACvBN,cAAc,EACdT,+BAA+B,CAAC,MAAM;MACpC,SAAS;;MACTU,OAAO,CAAC,GAAGG,IAAI,CAAC;IAClB,CAAC,CACH,CAAC;EACL;EACA,OAAO,CAAC,GAAGA,IAAI,KACbd,gBAAgB,CAACiB,iBAAiB,CAChCP,cAAc,EACdR,2BAA2B,CAAC,MAAM;IAChC,SAAS;;IACTS,OAAO,CAAC,GAAGG,IAAI,CAAC;EAClB,CAAC,CACH,CAAC;AACL", "ignoreList": []}