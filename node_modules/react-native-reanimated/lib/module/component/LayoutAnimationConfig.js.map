{"version": 3, "names": ["React", "Children", "Component", "createContext", "useEffect", "useRef", "setShouldAnimateExitingForTag", "isReact19", "findNodeHandle", "IS_REACT_19", "SkipEnteringContext", "SkipEntering", "props", "skipV<PERSON>ue<PERSON>ef", "shouldSkip", "current", "Provider", "children", "LayoutAnimationConfig", "getMaybeWrappedChildren", "count", "skipExiting", "map", "child", "setShouldAnimateExiting", "tag", "componentWillUnmount", "undefined", "render", "skipEntering"], "sourceRoot": "../../../src", "sources": ["component/LayoutAnimationConfig.tsx"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,KAAK,IACVC,QAAQ,EACRC,SAAS,EACTC,aAAa,EACbC,SAAS,EACTC,MAAM,QACD,OAAO;AAEd,SAASC,6BAA6B,QAAQ,YAAS;AACvD,SAASC,SAAS,QAAQ,uBAAoB;AAC9C,SAASC,cAAc,QAAQ,qCAAqC;AAEpE,MAAMC,WAAW,GAAGF,SAAS,CAAC,CAAC;AAE/B,OAAO,MAAMG,mBAAmB,GAC9BP,aAAa,CAAyC,IAAI,CAAC;;AAE7D;AACA;;AAOA,SAASQ,YAAYA,CAACC,KAAmD,EAAE;EACzE,MAAMC,YAAY,GAAGR,MAAM,CAACO,KAAK,CAACE,UAAU,CAAC;EAE7CV,SAAS,CAAC,MAAM;IACdS,YAAY,CAACE,OAAO,GAAG,KAAK;EAC9B,CAAC,EAAE,CAACF,YAAY,CAAC,CAAC;EAElB,MAAMG,QAAQ,GAAGP,WAAW,GACxBC,mBAAmB,GACnBA,mBAAmB,CAACM,QAAQ;EAEhC,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,CAACH,YAAY,CAAC,CAAC,CAACD,KAAK,CAACK,QAAQ,CAAC,EAAE,QAAQ,CAAC;AACnE;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,qBAAqB,SAAShB,SAAS,CAA6B;EAC/EiB,uBAAuBA,CAAA,EAAG;IACxB,OAAOlB,QAAQ,CAACmB,KAAK,CAAC,IAAI,CAACR,KAAK,CAACK,QAAQ,CAAC,GAAG,CAAC,IAAI,IAAI,CAACL,KAAK,CAACS,WAAW,GACpEpB,QAAQ,CAACqB,GAAG,CAAC,IAAI,CAACV,KAAK,CAACK,QAAQ,EAAGM,KAAK,IACtC,CAAC,qBAAqB,CAAC,WAAW,CAAC,CAACA,KAAK,CAAC,EAAE,qBAAqB,CAClE,CAAC,GACF,IAAI,CAACX,KAAK,CAACK,QAAQ;EACzB;EAEAO,uBAAuBA,CAAA,EAAG;IACxB,IAAIvB,QAAQ,CAACmB,KAAK,CAAC,IAAI,CAACR,KAAK,CAACK,QAAQ,CAAC,KAAK,CAAC,EAAE;MAC7C,MAAMQ,GAAG,GAAGjB,cAAc,CAAC,IAAI,CAAC;MAChC,IAAIiB,GAAG,EAAE;QACPnB,6BAA6B,CAACmB,GAAG,EAAE,CAAC,IAAI,CAACb,KAAK,CAACS,WAAW,CAAC;MAC7D;IACF;EACF;EAEAK,oBAAoBA,CAAA,EAAS;IAC3B,IAAI,IAAI,CAACd,KAAK,CAACS,WAAW,KAAKM,SAAS,EAAE;MACxC,IAAI,CAACH,uBAAuB,CAAC,CAAC;IAChC;EACF;EAEAI,MAAMA,CAAA,EAAc;IAClB,MAAMX,QAAQ,GAAG,IAAI,CAACE,uBAAuB,CAAC,CAAC;IAE/C,IAAI,IAAI,CAACP,KAAK,CAACiB,YAAY,KAAKF,SAAS,EAAE;MACzC,OAAOV,QAAQ;IACjB;IAEA,OACE,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC,IAAI,CAACL,KAAK,CAACiB,YAAY,CAAC;AACxD,QAAQ,CAACZ,QAAQ;AACjB,MAAM,EAAE,YAAY,CAAC;EAEnB;AACF", "ignoreList": []}