{"version": 3, "names": ["runOnUI", "prepareUIRegistry", "FrameCallbackRegistryJS", "nextCallbackId", "constructor", "registerFrameCallback", "callback", "callbackId", "global", "_frameCallbackRegistry", "unregisterFrameCallback", "manageStateFrameCallback", "state"], "sourceRoot": "../../../src", "sources": ["frameCallback/FrameCallbackRegistryJS.ts"], "mappings": "AAAA,YAAY;;AACZ,SAASA,OAAO,QAAQ,YAAS;AAEjC,SAASC,iBAAiB,QAAQ,8BAA2B;AAE7D,eAAe,MAAMC,uBAAuB,CAAC;EACnCC,cAAc,GAAG,CAAC;EAE1BC,WAAWA,CAAA,EAAG;IACZH,iBAAiB,CAAC,CAAC;EACrB;EAEAI,qBAAqBA,CAACC,QAAwC,EAAU;IACtE,IAAI,CAACA,QAAQ,EAAE;MACb,OAAO,CAAC,CAAC;IACX;IAEA,MAAMC,UAAU,GAAG,IAAI,CAACJ,cAAc;IACtC,IAAI,CAACA,cAAc,EAAE;IAErBH,OAAO,CAAC,MAAM;MACZQ,MAAM,CAACC,sBAAsB,CAACJ,qBAAqB,CAACC,QAAQ,EAAEC,UAAU,CAAC;IAC3E,CAAC,CAAC,CAAC,CAAC;IAEJ,OAAOA,UAAU;EACnB;EAEAG,uBAAuBA,CAACH,UAAkB,EAAQ;IAChDP,OAAO,CAAC,MAAM;MACZQ,MAAM,CAACC,sBAAsB,CAACC,uBAAuB,CAACH,UAAU,CAAC;IACnE,CAAC,CAAC,CAAC,CAAC;EACN;EAEAI,wBAAwBA,CAACJ,UAAkB,EAAEK,KAAc,EAAQ;IACjEZ,OAAO,CAAC,MAAM;MACZQ,MAAM,CAACC,sBAAsB,CAACE,wBAAwB,CAACJ,UAAU,EAAEK,KAAK,CAAC;IAC3E,CAAC,CAAC,CAAC,CAAC;EACN;AACF", "ignoreList": []}