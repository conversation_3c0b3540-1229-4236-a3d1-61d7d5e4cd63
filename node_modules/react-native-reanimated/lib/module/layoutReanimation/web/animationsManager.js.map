{"version": 3, "names": ["LayoutAnimationType", "EasingNameSymbol", "logger", "Keyframe", "makeElementVisible", "getProcessedConfig", "handleExitingAnimation", "handleLayoutTransition", "maybeModifyStyleForKeyframe", "setElementAnimation", "Animations", "createAnimationWithInitialValues", "createCustomKeyFrameAnimation", "areDOMRectsEqual", "chooseConfig", "animationType", "props", "config", "ENTERING", "entering", "EXITING", "exiting", "LAYOUT", "layout", "checkUndefinedAnimationFail", "initialAnimationName", "needsCustomization", "warn", "maybeReportOverwrittenProperties", "keyframe", "styles", "propertyRegex", "animationProperties", "Set", "match", "matchAll", "add", "commonProperties", "Array", "from", "filter", "style", "has", "length", "join", "chooseAction", "animationConfig", "element", "transitionData", "reversed", "tryGetAnimationConfig", "isLayoutTransition", "isCustomKeyframe", "hasInitialValues", "initialValues", "undefined", "animationName", "definitions", "presetName", "constructor", "shouldFail", "keyframeTimestamps", "Object", "keys", "includes", "startWebLayoutAnimation", "tryActivateLayoutTransition", "snapshot", "rect", "getBoundingClientRect", "enteringAnimation", "enteringV", "exitingAnimation", "exitingV", "deltaX", "width", "deltaY", "height", "translateX", "x", "translateY", "y", "scaleX", "scaleY", "easingX", "easingXV", "easingY", "easingYV"], "sourceRoot": "../../../../src", "sources": ["layoutReanimation/web/animationsManager.ts"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,mBAAmB,QAAQ,sBAAmB;AAKvD,SAASC,gBAAgB,QAAQ,iBAAc;AAC/C,SAASC,MAAM,QAAQ,uBAAc;AAErC,SAASC,QAAQ,QAAQ,8BAAqB;AAE9C,SAASC,kBAAkB,QAAQ,qBAAkB;AACrD,SACEC,kBAAkB,EAClBC,sBAAsB,EACtBC,sBAAsB,EACtBC,2BAA2B,EAC3BC,mBAAmB,QACd,qBAAkB;AAQzB,SAASC,UAAU,QAAQ,aAAU;AACrC,SACEC,gCAAgC,EAChCC,6BAA6B,QACxB,sBAAmB;AAC1B,SAASC,gBAAgB,QAAQ,eAAY;AAE7C,SAASC,YAAYA,CACnBC,aAAkC,EAClCC,KAAuD,EACvD;EACA,MAAMC,MAAM,GACVF,aAAa,KAAKf,mBAAmB,CAACkB,QAAQ,GAC1CF,KAAK,CAACG,QAAQ,GACdJ,aAAa,KAAKf,mBAAmB,CAACoB,OAAO,GAC3CJ,KAAK,CAACK,OAAO,GACbN,aAAa,KAAKf,mBAAmB,CAACsB,MAAM,GAC1CN,KAAK,CAACO,MAAM,GACZ,IAAI;EAEd,OAAON,MAAM;AACf;AAEA,SAASO,2BAA2BA,CAClCC,oBAA4B,EAC5BC,kBAA2B,EAC3B;EACA;EACA;EACA,IAAID,oBAAoB,IAAIf,UAAU,IAAIgB,kBAAkB,EAAE;IAC5D,OAAO,KAAK;EACd;EAEAxB,MAAM,CAACyB,IAAI,CACT,qLACF,CAAC;EAED,OAAO,IAAI;AACb;AAEA,SAASC,gCAAgCA,CACvCC,QAAgB,EAChBC,MAA2B,EAC3B;EACA,MAAMC,aAAa,GAAG,oBAAoB;EAC1C,MAAMC,mBAAmB,GAAG,IAAIC,GAAG,CAAC,CAAC;EAErC,KAAK,MAAMC,KAAK,IAAIL,QAAQ,CAACM,QAAQ,CAACJ,aAAa,CAAC,EAAE;IACpDC,mBAAmB,CAACI,GAAG,CAACF,KAAK,CAAC,CAAC,CAAC,CAAC;EACnC;EAEA,MAAMG,gBAAgB,GAAGC,KAAK,CAACC,IAAI,CAACT,MAAM,CAAC,CAACU,MAAM,CAAEC,KAAK,IACvDT,mBAAmB,CAACU,GAAG,CAACD,KAAK,CAC/B,CAAC;EAED,IAAIJ,gBAAgB,CAACM,MAAM,KAAK,CAAC,EAAE;IACjC;EACF;EAEAzC,MAAM,CAACyB,IAAI,CACT,GACEU,gBAAgB,CAACM,MAAM,KAAK,CAAC,GAAG,UAAU,GAAG,YAAY,KACtDN,gBAAgB,CAACO,IAAI,CACxB,IACF,CAAC,6IACH,CAAC;AACH;AAEA,SAASC,YAAYA,CACnB9B,aAAkC,EAClC+B,eAAgC,EAChCC,OAA8B,EAC9BC,cAA8B,EAC9B;EACA,QAAQjC,aAAa;IACnB,KAAKf,mBAAmB,CAACkB,QAAQ;MAC/BT,mBAAmB,CAACsC,OAAO,EAAED,eAAe,EAAE,IAAI,CAAC;MACnD;IACF,KAAK9C,mBAAmB,CAACsB,MAAM;MAC7B0B,cAAc,CAACC,QAAQ,GAAGH,eAAe,CAACG,QAAQ;MAClD1C,sBAAsB,CAACwC,OAAO,EAAED,eAAe,EAAEE,cAAc,CAAC;MAChE;IACF,KAAKhD,mBAAmB,CAACoB,OAAO;MAC9Bd,sBAAsB,CAACyC,OAAO,EAAED,eAAe,CAAC;MAChD;EACJ;AACF;AAEA,SAASI,qBAAqBA,CAC5BlC,KAAuD,EACvDD,aAAkC,EAClC;EACA,MAAME,MAAM,GAAGH,YAAY,CAACC,aAAa,EAAEC,KAAK,CAAC;EACjD,IAAI,CAACC,MAAM,EAAE;IACX,OAAO,IAAI;EACb;EAKA,MAAMkC,kBAAkB,GAAGpC,aAAa,KAAKf,mBAAmB,CAACsB,MAAM;EACvE,MAAM8B,gBAAgB,GAAGnC,MAAM,YAAYd,QAAQ;EACnD,MAAMkD,gBAAgB,GAAIpC,MAAM,CAAkBqC,aAAa,KAAKC,SAAS;EAE7E,IAAIC,aAAa;EAEjB,IAAIJ,gBAAgB,EAAE;IACpBI,aAAa,GAAG5C,6BAA6B,CAC1CK,MAAM,CAAkBwC,WAC3B,CAAC;EACH,CAAC,MAAM,IAAI,OAAOxC,MAAM,KAAK,UAAU,EAAE;IACvCuC,aAAa,GAAGvC,MAAM,CAACyC,UAAU;EACnC,CAAC,MAAM;IACLF,aAAa,GAAIvC,MAAM,CAAC0C,WAAW,CAChCD,UAAU;EACf;EAEA,IAAIL,gBAAgB,EAAE;IACpBG,aAAa,GAAG7C,gCAAgC,CAC9C6C,aAAa,EACZvC,MAAM,CAAkBqC,aAC3B,CAAC;EACH;EAEA,MAAMM,UAAU,GAAGpC,2BAA2B,CAC5CgC,aAAa,EACbL,kBAAkB,IAAIC,gBAAgB,IAAIC,gBAC5C,CAAC;EAED,IAAIO,UAAU,EAAE;IACd,OAAO,IAAI;EACb;EAEA,IAAIR,gBAAgB,EAAE;IACpB,MAAMS,kBAAkB,GAAGC,MAAM,CAACC,IAAI,CACnC9C,MAAM,CAAkBwC,WAC3B,CAAC;IAED,IACE,EAAEI,kBAAkB,CAACG,QAAQ,CAAC,KAAK,CAAC,IAAIH,kBAAkB,CAACG,QAAQ,CAAC,IAAI,CAAC,CAAC,EAC1E;MACA9D,MAAM,CAACyB,IAAI,CACT,+MACF,CAAC;IACH;EACF;EAEA,MAAMmB,eAAe,GAAGzC,kBAAkB,CACxCmD,aAAa,EACbzC,aAAa,EACbE,MACF,CAAC;EAED,OAAO6B,eAAe;AACxB;AAEA,OAAO,SAASmB,uBAAuBA,CAGrCjD,KAAuD,EACvD+B,OAA8B,EAC9BhC,aAAkC,EAClCiC,cAA+B,EAC/B;EACA,MAAMF,eAAe,GAAGI,qBAAqB,CAAClC,KAAK,EAAED,aAAa,CAAC;EAEnEP,2BAA2B,CAACuC,OAAO,EAAE/B,KAAK,CAACG,QAAwB,CAAC;EAEpE,IAAK2B,eAAe,EAAEU,aAAa,IAAuB9C,UAAU,EAAE;IACpEkB,gCAAgC,CAC9BlB,UAAU,CAACoC,eAAe,EAAEU,aAAa,CAAmB,CAACf,KAAK,EAClEM,OAAO,CAACN,KACV,CAAC;EACH;EAEA,IAAIK,eAAe,EAAE;IACnBD,YAAY,CACV9B,aAAa,EACb+B,eAAe,EACfC,OAAO,EACPC,cACF,CAAC;EACH,CAAC,MAAM;IACL5C,kBAAkB,CAAC2C,OAAO,EAAE,CAAC,CAAC;EAChC;AACF;AAEA,OAAO,SAASmB,2BAA2BA,CAGzClD,KAAuD,EACvD+B,OAA8B,EAC9BoB,QAAiB,EACjB;EACA,IAAI,CAACnD,KAAK,CAACO,MAAM,EAAE;IACjB;EACF;EAEA,MAAM6C,IAAI,GAAGrB,OAAO,CAACsB,qBAAqB,CAAC,CAAC;EAE5C,IAAIxD,gBAAgB,CAACuD,IAAI,EAAED,QAAQ,CAAC,EAAE;IACpC;EACF;EAEA,MAAMG,iBAAiB,GAAItD,KAAK,CAACO,MAAM,CAAkBgD,SAAS,EAC9Db,UAAU;EACd,MAAMc,gBAAgB,GAAIxD,KAAK,CAACO,MAAM,CAAkBkD,QAAQ,EAAEf,UAAU;EAE5E,MAAMgB,MAAM,GAAG,CAACP,QAAQ,CAACQ,KAAK,GAAGP,IAAI,CAACO,KAAK,IAAI,CAAC;EAChD,MAAMC,MAAM,GAAG,CAACT,QAAQ,CAACU,MAAM,GAAGT,IAAI,CAACS,MAAM,IAAI,CAAC;EAClD,MAAM7B,cAA8B,GAAG;IACrC8B,UAAU,EAAEX,QAAQ,CAACY,CAAC,GAAGX,IAAI,CAACW,CAAC,GAAGL,MAAM;IACxCM,UAAU,EAAEb,QAAQ,CAACc,CAAC,GAAGb,IAAI,CAACa,CAAC,GAAGL,MAAM;IACxCM,MAAM,EAAEf,QAAQ,CAACQ,KAAK,GAAGP,IAAI,CAACO,KAAK;IACnCQ,MAAM,EAAEhB,QAAQ,CAACU,MAAM,GAAGT,IAAI,CAACS,MAAM;IACrC5B,QAAQ,EAAE,KAAK;IAAE;IACjBmC,OAAO,EACJpE,KAAK,CAACO,MAAM,CAAkB8D,QAAQ,GAAGpF,gBAAgB,CAAC,IAAI,MAAM;IACvEqF,OAAO,EACJtE,KAAK,CAACO,MAAM,CAAkBgE,QAAQ,GAAGtF,gBAAgB,CAAC,IAAI,MAAM;IACvEkB,QAAQ,EAAEmD,iBAAiB;IAC3BjD,OAAO,EAAEmD;EACX,CAAC;EAEDP,uBAAuB,CACrBjD,KAAK,EACL+B,OAAO,EACP/C,mBAAmB,CAACsB,MAAM,EAC1B0B,cACF,CAAC;AACH", "ignoreList": []}