{"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON>", "getReduceMotionFromConfig", "ReduceMotion", "ReanimatedError", "BaseAnimationBuilder", "reduceMotionV", "System", "randomizeDelay", "build", "duration", "durationMs", "instance", "createInstance", "durationV", "delay", "delayMs", "delayV", "<PERSON><PERSON><PERSON><PERSON>", "callback", "callbackV", "reduceMotion", "getDuration", "randomDelay", "get<PERSON>elay", "Math", "random", "getReduceMotion", "getDelayFunction", "isDelayProvided", "animation", "_"], "sourceRoot": "../../../../src", "sources": ["layoutReanimation/animationBuilder/BaseAnimationBuilder.ts"], "mappings": "AAAA,YAAY;;AACZ,SAASA,SAAS,QAAQ,0BAAiB;AAC3C,SAASC,yBAAyB,QAAQ,yBAAsB;AAMhE,SAASC,YAAY,QAAQ,sBAAmB;AAChD,SAASC,eAAe,QAAQ,iBAAc;AAE9C,OAAO,MAAMC,oBAAoB,CAAC;EAGhCC,aAAa,GAAiBH,YAAY,CAACI,MAAM;EACjDC,cAAc,GAAG,KAAK;EAOtBC,KAAK,GAAGA,CAAA,KAA4D;IAClE,MAAM,IAAIL,eAAe,CAAC,sCAAsC,CAAC;EACnE,CAAC;;EAED;AACF;AACA;AACA;AACA;AACA;AACA;EACE,OAAOM,QAAQA,CAEbC,UAAkB,EACD;IACjB,MAAMC,QAAQ,GAAG,IAAI,CAACC,cAAc,CAAC,CAAC;IACtC,OAAOD,QAAQ,CAACF,QAAQ,CAACC,UAAU,CAAC;EACtC;EAEAD,QAAQA,CAACC,UAAkB,EAAQ;IACjC,IAAI,CAACG,SAAS,GAAGH,UAAU;IAC3B,OAAO,IAAI;EACb;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;EACE,OAAOI,KAAKA,CAEVC,OAAe,EACE;IACjB,MAAMJ,QAAQ,GAAG,IAAI,CAACC,cAAc,CAAC,CAAC;IACtC,OAAOD,QAAQ,CAACG,KAAK,CAACC,OAAO,CAAC;EAChC;EAEAD,KAAKA,CAACC,OAAe,EAAQ;IAC3B,IAAI,CAACC,MAAM,GAAGD,OAAO;IACrB,OAAO,IAAI;EACb;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;EACE,OAAOE,YAAYA,CAEjBC,QAAqC,EACpB;IACjB,MAAMP,QAAQ,GAAG,IAAI,CAACC,cAAc,CAAC,CAAC;IACtC,OAAOD,QAAQ,CAACM,YAAY,CAACC,QAAQ,CAAC;EACxC;EAEAD,YAAYA,CAACC,QAAqC,EAAQ;IACxD,IAAI,CAACC,SAAS,GAAGD,QAAQ;IACzB,OAAO,IAAI;EACb;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,OAAOE,YAAYA,CAEjBA,YAA0B,EACT;IACjB,MAAMT,QAAQ,GAAG,IAAI,CAACC,cAAc,CAAC,CAAC;IACtC,OAAOD,QAAQ,CAACS,YAAY,CAACA,YAAY,CAAC;EAC5C;EAEAA,YAAYA,CAACf,aAA2B,EAAQ;IAC9C,IAAI,CAACA,aAAa,GAAGA,aAAa;IAClC,OAAO,IAAI;EACb;;EAEA;EACA,OAAOgB,WAAWA,CAAA,EAAW;IAC3B,OAAO,GAAG;EACZ;EAEAA,WAAWA,CAAA,EAAW;IACpB,OAAO,IAAI,CAACR,SAAS,IAAI,GAAG;EAC9B;;EAEA;EACA,OAAOS,WAAWA,CAAA,EAEC;IACjB,MAAMX,QAAQ,GAAG,IAAI,CAACC,cAAc,CAAC,CAAC;IACtC,OAAOD,QAAQ,CAACW,WAAW,CAAC,CAAC;EAC/B;EAEAA,WAAWA,CAAA,EAAS;IAClB,IAAI,CAACf,cAAc,GAAG,IAAI;IAC1B,OAAO,IAAI;EACb;;EAEA;EACAgB,QAAQA,CAAA,EAAW;IACjB,OAAO,IAAI,CAAChB,cAAc,GACtBiB,IAAI,CAACC,MAAM,CAAC,CAAC,IAAI,IAAI,CAACT,MAAM,IAAI,IAAI,CAAC,GACpC,IAAI,CAACA,MAAM,IAAI,CAAE;EACxB;EAEAU,eAAeA,CAAA,EAAiB;IAC9B,OAAO,IAAI,CAACrB,aAAa;EAC3B;EAEAsB,gBAAgBA,CAAA,EAAsB;IACpC,MAAMC,eAAe,GAAG,IAAI,CAACrB,cAAc,IAAI,IAAI,CAACS,MAAM;IAC1D,MAAMI,YAAY,GAAG,IAAI,CAACM,eAAe,CAAC,CAAC;IAC3C,OAAOE,eAAe,GAClB,CAACd,KAAK,EAAEe,SAAS,KAAK;MACpB,SAAS;;MACT,OAAO7B,SAAS,CAACc,KAAK,EAAEe,SAAS,EAAET,YAAY,CAAC;IAClD,CAAC,GACD,CAACU,CAAC,EAAED,SAAS,KAAK;MAChB,SAAS;;MACTA,SAAS,CAACT,YAAY,GAAGnB,yBAAyB,CAACmB,YAAY,CAAC;MAChE,OAAOS,SAAS;IAClB,CAAC;EACP;EAEA,OAAOrB,KAAKA,CAAA,EAE4C;IACtD,MAAMG,QAAQ,GAAG,IAAI,CAACC,cAAc,CAAC,CAAC;IACtC,OAAOD,QAAQ,CAACH,KAAK,CAAC,CAAC;EACzB;AACF", "ignoreList": []}