{"version": 3, "names": ["useEffect", "useState", "cancelAnimation", "makeMutable", "useSharedValue", "initialValue", "mutable"], "sourceRoot": "../../../src", "sources": ["hook/useSharedValue.ts"], "mappings": "AAAA,YAAY;;AACZ,SAASA,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAE3C,SAASC,eAAe,QAAQ,uBAAc;AAE9C,SAASC,WAAW,QAAQ,YAAS;;AAErC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,cAAcA,CAAQC,YAAmB,EAAsB;EAC7E,MAAM,CAACC,OAAO,CAAC,GAAGL,QAAQ,CAAC,MAAME,WAAW,CAACE,YAAY,CAAC,CAAC;EAC3DL,SAAS,CAAC,MAAM;IACd,OAAO,MAAM;MACXE,eAAe,CAACI,OAAO,CAAC;IAC1B,CAAC;EACH,CAAC,EAAE,CAACA,OAAO,CAAC,CAAC;EACb,OAAOA,OAAO;AAChB", "ignoreList": []}